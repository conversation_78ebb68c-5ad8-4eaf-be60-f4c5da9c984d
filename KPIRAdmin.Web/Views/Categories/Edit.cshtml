@model CategoryFormDto
@{
    ViewData["Title"] = "Edit Category";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Category</h1>
    <div class="btn-group" role="group">
        <a asp-action="Details" asp-route-id="@Model.CategoryId" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i>View Details
        </a>
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <form asp-action="Edit" method="post">
            @Html.AntiForgeryToken()
            <input asp-for="CategoryId" type="hidden" />
            
            <!-- Validation Summary -->
            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Basic Information</h5>
                    <small class="text-muted">ID: @Model.CategoryId</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="CategoryEnglish" class="form-label"></label>
                            <input asp-for="CategoryEnglish" class="form-control" placeholder="Enter category name in English">
                            <span asp-validation-for="CategoryEnglish" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="CategoryFrench" class="form-label"></label>
                            <input asp-for="CategoryFrench" class="form-control" placeholder="Enter category name in French">
                            <span asp-validation-for="CategoryFrench" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="SortOrderEnglish" class="form-label"></label>
                            <input asp-for="SortOrderEnglish" class="form-control" type="number" min="0" placeholder="0">
                            <span asp-validation-for="SortOrderEnglish" class="text-danger"></span>
                            <div class="form-text">Lower numbers appear first in English sorting</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="SortOrderFrench" class="form-label"></label>
                            <input asp-for="SortOrderFrench" class="form-control" type="number" min="0" placeholder="0">
                            <span asp-validation-for="SortOrderFrench" class="text-danger"></span>
                            <div class="form-text">Lower numbers appear first in French sorting</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input asp-for="ActiveFlag" class="form-check-input" type="checkbox">
                                <label asp-for="ActiveFlag" class="form-check-label"></label>
                                <span asp-validation-for="ActiveFlag" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="KpirFlag" class="form-label">KPIR Flag</label>
                            <select asp-for="KpirFlag" class="form-select">
                                <option value="">Not Set</option>
                                <option value="true">KPIR</option>
                                <option value="false">Non-KPIR</option>
                            </select>
                            <span asp-validation-for="KpirFlag" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Short Descriptions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Short Descriptions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="ShortDescriptionEnglish" class="form-label"></label>
                            <textarea asp-for="ShortDescriptionEnglish" class="form-control" rows="3" placeholder="Brief description in English"></textarea>
                            <span asp-validation-for="ShortDescriptionEnglish" class="text-danger"></span>
                            <div class="form-text">Maximum 500 characters</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="ShortDescriptionFrench" class="form-label"></label>
                            <textarea asp-for="ShortDescriptionFrench" class="form-control" rows="3" placeholder="Brief description in French"></textarea>
                            <span asp-validation-for="ShortDescriptionFrench" class="text-danger"></span>
                            <div class="form-text">Maximum 500 characters</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Full Descriptions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Full Descriptions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="DescriptionEnglish" class="form-label"></label>
                            <textarea asp-for="DescriptionEnglish" class="form-control" rows="6" placeholder="Detailed description in English"></textarea>
                            <span asp-validation-for="DescriptionEnglish" class="text-danger"></span>
                            <div class="form-text">Maximum 5000 characters</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="DescriptionFrench" class="form-label"></label>
                            <textarea asp-for="DescriptionFrench" class="form-control" rows="6" placeholder="Detailed description in French"></textarea>
                            <span asp-validation-for="DescriptionFrench" class="text-danger"></span>
                            <div class="form-text">Maximum 5000 characters</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Changes
                            </button>
                            <button type="reset" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-undo me-1"></i>Reset Form
                            </button>
                        </div>
                        <div>
                            <a asp-action="Details" asp-route-id="@Model.CategoryId" class="btn btn-outline-info me-2">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="col-lg-4">
        <!-- Current Status -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Current Status</h5>
            </div>
            <div class="card-body">
                <dl class="row mb-0">
                    <dt class="col-6">Status:</dt>
                    <dd class="col-6">
                        @if (Model.ActiveFlag)
                        {
                            <span class="badge bg-success">Active</span>
                        }
                        else
                        {
                            <span class="badge bg-warning">Inactive</span>
                        }
                    </dd>
                    <dt class="col-6">KPIR:</dt>
                    <dd class="col-6">
                        @if (Model.KpirFlag == true)
                        {
                            <span class="badge bg-info">KPIR</span>
                        }
                        else if (Model.KpirFlag == false)
                        {
                            <span class="badge bg-secondary">Non-KPIR</span>
                        }
                        else
                        {
                            <span class="badge bg-light text-dark">Not Set</span>
                        }
                    </dd>
                </dl>
            </div>
        </div>

        <!-- Help Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <h6>Required Fields</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-1"></i>Active Flag is required</li>
                </ul>

                <h6>Field Guidelines</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-info-circle text-info me-1"></i>Category names: 50 characters max</li>
                    <li><i class="fas fa-info-circle text-info me-1"></i>Short descriptions: 500 characters max</li>
                    <li><i class="fas fa-info-circle text-info me-1"></i>Full descriptions: 5000 characters max</li>
                    <li><i class="fas fa-info-circle text-info me-1"></i>Sort orders: Positive numbers only</li>
                </ul>

                <h6>Actions</h6>
                <div class="d-grid gap-2">
                    <a asp-action="Delete" asp-route-id="@Model.CategoryId" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash me-1"></i>Delete Category
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Character count for textareas
        $('textarea').on('input', function() {
            var maxLength = $(this).attr('maxlength');
            if (maxLength) {
                var currentLength = $(this).val().length;
                var remaining = maxLength - currentLength;
                var helpText = $(this).siblings('.form-text');
                if (helpText.length) {
                    helpText.text('Maximum ' + maxLength + ' characters (' + remaining + ' remaining)');
                    if (remaining < 50) {
                        helpText.addClass('text-warning');
                    } else {
                        helpText.removeClass('text-warning');
                    }
                }
            }
        });

        // Initialize character counts
        $('textarea').trigger('input');
    </script>
}

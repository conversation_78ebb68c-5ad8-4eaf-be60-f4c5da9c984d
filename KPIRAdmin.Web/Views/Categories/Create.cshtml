@model CategoryFormDto
@{
    ViewData["Title"] = "Create Category";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Create New Category</h1>
    <a asp-action="Index" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Back to List
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <form asp-action="Create" method="post">
            @Html.AntiForgeryToken()
            
            <!-- Validation Summary -->
            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="CategoryEnglish" class="form-label"></label>
                            <input asp-for="CategoryEnglish" class="form-control" placeholder="Enter category name in English">
                            <span asp-validation-for="CategoryEnglish" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="CategoryFrench" class="form-label"></label>
                            <input asp-for="CategoryFrench" class="form-control" placeholder="Enter category name in French">
                            <span asp-validation-for="CategoryFrench" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="SortOrderEnglish" class="form-label"></label>
                            <input asp-for="SortOrderEnglish" class="form-control" type="number" min="0" placeholder="0">
                            <span asp-validation-for="SortOrderEnglish" class="text-danger"></span>
                            <div class="form-text">Lower numbers appear first in English sorting</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="SortOrderFrench" class="form-label"></label>
                            <input asp-for="SortOrderFrench" class="form-control" type="number" min="0" placeholder="0">
                            <span asp-validation-for="SortOrderFrench" class="text-danger"></span>
                            <div class="form-text">Lower numbers appear first in French sorting</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input asp-for="ActiveFlag" class="form-check-input" type="checkbox">
                                <label asp-for="ActiveFlag" class="form-check-label"></label>
                                <span asp-validation-for="ActiveFlag" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="KpirFlag" class="form-label">KPIR Flag</label>
                            <select asp-for="KpirFlag" class="form-select">
                                <option value="">Not Set</option>
                                <option value="true">KPIR</option>
                                <option value="false">Non-KPIR</option>
                            </select>
                            <span asp-validation-for="KpirFlag" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Short Descriptions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Short Descriptions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="ShortDescriptionEnglish" class="form-label"></label>
                            <textarea asp-for="ShortDescriptionEnglish" class="form-control" rows="3" placeholder="Brief description in English"></textarea>
                            <span asp-validation-for="ShortDescriptionEnglish" class="text-danger"></span>
                            <div class="form-text">Maximum 500 characters</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="ShortDescriptionFrench" class="form-label"></label>
                            <textarea asp-for="ShortDescriptionFrench" class="form-control" rows="3" placeholder="Brief description in French"></textarea>
                            <span asp-validation-for="ShortDescriptionFrench" class="text-danger"></span>
                            <div class="form-text">Maximum 500 characters</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Full Descriptions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Full Descriptions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="DescriptionEnglish" class="form-label"></label>
                            <textarea asp-for="DescriptionEnglish" class="form-control" rows="6" placeholder="Detailed description in English"></textarea>
                            <span asp-validation-for="DescriptionEnglish" class="text-danger"></span>
                            <div class="form-text">Maximum 5000 characters</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="DescriptionFrench" class="form-label"></label>
                            <textarea asp-for="DescriptionFrench" class="form-control" rows="6" placeholder="Detailed description in French"></textarea>
                            <span asp-validation-for="DescriptionFrench" class="text-danger"></span>
                            <div class="form-text">Maximum 5000 characters</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Create Category
                            </button>
                            <button type="reset" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-undo me-1"></i>Reset Form
                            </button>
                        </div>
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="col-lg-4">
        <!-- Help Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <h6>Required Fields</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-1"></i>Active Flag is required</li>
                </ul>

                <h6>Field Guidelines</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-info-circle text-info me-1"></i>Category names: 50 characters max</li>
                    <li><i class="fas fa-info-circle text-info me-1"></i>Short descriptions: 500 characters max</li>
                    <li><i class="fas fa-info-circle text-info me-1"></i>Full descriptions: 5000 characters max</li>
                    <li><i class="fas fa-info-circle text-info me-1"></i>Sort orders: Positive numbers only</li>
                </ul>

                <h6>KPIR Flag</h6>
                <p class="small text-muted">
                    The KPIR flag indicates whether this category is part of the KPIR system. 
                    Leave as "Not Set" if unsure.
                </p>

                <h6>Bilingual Support</h6>
                <p class="small text-muted">
                    You can provide content in both English and French. 
                    At least one language should be provided for the category name.
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Character count for textareas
        $('textarea').on('input', function() {
            var maxLength = $(this).attr('maxlength');
            if (maxLength) {
                var currentLength = $(this).val().length;
                var remaining = maxLength - currentLength;
                var helpText = $(this).siblings('.form-text');
                if (helpText.length) {
                    helpText.text('Maximum ' + maxLength + ' characters (' + remaining + ' remaining)');
                    if (remaining < 50) {
                        helpText.addClass('text-warning');
                    } else {
                        helpText.removeClass('text-warning');
                    }
                }
            }
        });

        // Auto-fill French sort order when English is entered
        $('#SortOrderEnglish').on('input', function() {
            var frenchSortOrder = $('#SortOrderFrench');
            if (frenchSortOrder.val() === '') {
                frenchSortOrder.val($(this).val());
            }
        });
    </script>
}

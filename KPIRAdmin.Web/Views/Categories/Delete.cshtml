@model CategoryDto
@{
    ViewData["Title"] = "Delete Category";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="text-danger">Delete Category</h1>
    <a asp-action="Index" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Back to List
    </a>
</div>

<div class="alert alert-danger" role="alert">
    <h4 class="alert-heading">
        <i class="fas fa-exclamation-triangle me-2"></i>Warning!
    </h4>
    <p>You are about to permanently delete this category. This action cannot be undone.</p>
    <hr>
    <p class="mb-0">
        <strong>Consider using "Deactivate" instead</strong> if you want to keep the data but make it inactive.
    </p>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Category Information -->
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">Category to be Deleted</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">ID:</dt>
                    <dd class="col-sm-9">@Model.CategoryId</dd>

                    <dt class="col-sm-3">Category (English):</dt>
                    <dd class="col-sm-9">@(Model.CategoryEnglish ?? "Not specified")</dd>

                    <dt class="col-sm-3">Category (French):</dt>
                    <dd class="col-sm-9">@(Model.CategoryFrench ?? "Not specified")</dd>

                    <dt class="col-sm-3">Status:</dt>
                    <dd class="col-sm-9">
                        @if (Model.ActiveFlag)
                        {
                            <span class="badge bg-success">Active</span>
                        }
                        else
                        {
                            <span class="badge bg-warning">Inactive</span>
                        }
                    </dd>

                    <dt class="col-sm-3">KPIR Flag:</dt>
                    <dd class="col-sm-9">
                        @if (Model.KpirFlag == true)
                        {
                            <span class="badge bg-info">KPIR</span>
                        }
                        else if (Model.KpirFlag == false)
                        {
                            <span class="badge bg-secondary">Non-KPIR</span>
                        }
                        else
                        {
                            <span class="badge bg-light text-dark">Not Set</span>
                        }
                    </dd>
                </dl>

                @if (!string.IsNullOrEmpty(Model.ShortDescriptionEnglish) || !string.IsNullOrEmpty(Model.ShortDescriptionFrench))
                {
                    <hr>
                    <h6>Short Descriptions:</h6>
                    @if (!string.IsNullOrEmpty(Model.ShortDescriptionEnglish))
                    {
                        <p><strong>English:</strong> @Model.ShortDescriptionEnglish</p>
                    }
                    @if (!string.IsNullOrEmpty(Model.ShortDescriptionFrench))
                    {
                        <p><strong>French:</strong> @Model.ShortDescriptionFrench</p>
                    }
                }

                @if (!string.IsNullOrEmpty(Model.DescriptionEnglish) || !string.IsNullOrEmpty(Model.DescriptionFrench))
                {
                    <hr>
                    <h6>Full Descriptions:</h6>
                    @if (!string.IsNullOrEmpty(Model.DescriptionEnglish))
                    {
                        <div class="mb-2">
                            <strong>English:</strong>
                            <div class="border rounded p-2 bg-light">
                                @Html.Raw(Model.DescriptionEnglish.Replace("\n", "<br>"))
                            </div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(Model.DescriptionFrench))
                    {
                        <div>
                            <strong>French:</strong>
                            <div class="border rounded p-2 bg-light">
                                @Html.Raw(Model.DescriptionFrench.Replace("\n", "<br>"))
                            </div>
                        </div>
                    }
                }
            </div>
        </div>

        <!-- Confirmation Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Confirm Deletion</h5>
            </div>
            <div class="card-body">
                <form asp-action="Delete" method="post">
                    @Html.AntiForgeryToken()
                    <input asp-for="CategoryId" type="hidden" />
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <p class="mb-0">
                                <strong>Are you sure you want to delete this category?</strong>
                            </p>
                            <small class="text-muted">This action cannot be undone.</small>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-danger me-2">
                                <i class="fas fa-trash me-1"></i>Yes, Delete
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.CategoryId" class="btn btn-outline-info me-2">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Alternative Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Alternative Actions</h5>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-3">
                    Consider these alternatives to permanent deletion:
                </p>
                
                <div class="d-grid gap-2">
                    @if (Model.ActiveFlag)
                    {
                        <form asp-action="SoftDelete" asp-route-id="@Model.CategoryId" method="post" class="d-inline">
                            @Html.AntiForgeryToken()
                            <button type="submit" class="btn btn-warning w-100">
                                <i class="fas fa-pause me-1"></i>Deactivate Instead
                            </button>
                        </form>
                        <small class="text-muted">Deactivate to keep data but make it inactive</small>
                    }
                    else
                    {
                        <div class="alert alert-info py-2">
                            <small>This category is already inactive</small>
                        </div>
                    }
                    
                    <hr>
                    
                    <a asp-action="Edit" asp-route-id="@Model.CategoryId" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-1"></i>Edit Instead
                    </a>
                    
                    <a asp-action="Details" asp-route-id="@Model.CategoryId" class="btn btn-outline-info">
                        <i class="fas fa-eye me-1"></i>View Details
                    </a>
                </div>
            </div>
        </div>

        <!-- Deletion Impact -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Deletion Impact</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning py-2">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>Data Loss:</strong> All category information will be permanently removed.
                    </small>
                </div>
                
                <div class="alert alert-info py-2">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        <strong>Related Records:</strong> Check for any dependencies before deletion.
                    </small>
                </div>
                
                <h6 class="small">What will be deleted:</h6>
                <ul class="small">
                    <li>Category names (English & French)</li>
                    <li>All descriptions</li>
                    <li>Sort order settings</li>
                    <li>KPIR flag settings</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Add confirmation dialog for delete button
        $('form[asp-action="Delete"] button[type="submit"]').click(function(e) {
            if (!confirm('Are you absolutely sure you want to permanently delete this category? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
        });
    </script>
}

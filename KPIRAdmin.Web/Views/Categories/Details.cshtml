@model CategoryDto
@{
    ViewData["Title"] = "Category Details";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Category Details</h1>
    <div class="btn-group" role="group">
        <a asp-action="Edit" asp-route-id="@Model.CategoryId" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>Edit
        </a>
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Back to List
        </a>
    </div>
</div>

<!-- Alert Messages -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="row">
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Basic Information</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">ID:</dt>
                    <dd class="col-sm-9">@Model.CategoryId</dd>

                    <dt class="col-sm-3">Category (English):</dt>
                    <dd class="col-sm-9">@(Model.CategoryEnglish ?? "Not specified")</dd>

                    <dt class="col-sm-3">Category (French):</dt>
                    <dd class="col-sm-9">@(Model.CategoryFrench ?? "Not specified")</dd>

                    <dt class="col-sm-3">Status:</dt>
                    <dd class="col-sm-9">
                        @if (Model.ActiveFlag)
                        {
                            <span class="badge bg-success">Active</span>
                        }
                        else
                        {
                            <span class="badge bg-warning">Inactive</span>
                        }
                    </dd>

                    <dt class="col-sm-3">KPIR Flag:</dt>
                    <dd class="col-sm-9">
                        @if (Model.KpirFlag == true)
                        {
                            <span class="badge bg-info">KPIR</span>
                        }
                        else if (Model.KpirFlag == false)
                        {
                            <span class="badge bg-secondary">Non-KPIR</span>
                        }
                        else
                        {
                            <span class="badge bg-light text-dark">Not Set</span>
                        }
                    </dd>
                </dl>
            </div>
        </div>

        <!-- Descriptions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Descriptions</h5>
            </div>
            <div class="card-body">
                <!-- English Descriptions -->
                <div class="mb-4">
                    <h6 class="text-primary">English</h6>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">Short Description:</label>
                            <div class="border rounded p-2 bg-light">
                                @(Model.ShortDescriptionEnglish ?? "Not specified")
                            </div>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">Full Description:</label>
                            <div class="border rounded p-2 bg-light" style="min-height: 100px;">
                                @if (!string.IsNullOrEmpty(Model.DescriptionEnglish))
                                {
                                    @Html.Raw(Model.DescriptionEnglish.Replace("\n", "<br>"))
                                }
                                else
                                {
                                    <span class="text-muted">Not specified</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- French Descriptions -->
                <div>
                    <h6 class="text-primary">French</h6>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">Short Description:</label>
                            <div class="border rounded p-2 bg-light">
                                @(Model.ShortDescriptionFrench ?? "Not specified")
                            </div>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">Full Description:</label>
                            <div class="border rounded p-2 bg-light" style="min-height: 100px;">
                                @if (!string.IsNullOrEmpty(Model.DescriptionFrench))
                                {
                                    @Html.Raw(Model.DescriptionFrench.Replace("\n", "<br>"))
                                }
                                else
                                {
                                    <span class="text-muted">Not specified</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Sort Orders -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Sort Orders</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-6">English:</dt>
                    <dd class="col-6">@(Model.SortOrderEnglish?.ToString() ?? "Not set")</dd>

                    <dt class="col-6">French:</dt>
                    <dd class="col-6">@(Model.SortOrderFrench?.ToString() ?? "Not set")</dd>
                </dl>
            </div>
        </div>

        <!-- Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Edit" asp-route-id="@Model.CategoryId" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i>Edit Category
                    </a>
                    
                    @if (Model.ActiveFlag)
                    {
                        <form asp-action="SoftDelete" asp-route-id="@Model.CategoryId" method="post" class="d-inline">
                            @Html.AntiForgeryToken()
                            <button type="submit" class="btn btn-warning w-100" onclick="return confirm('Are you sure you want to deactivate this category?')">
                                <i class="fas fa-pause me-1"></i>Deactivate
                            </button>
                        </form>
                    }
                    
                    <a asp-action="Delete" asp-route-id="@Model.CategoryId" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-1"></i>Delete
                    </a>
                    
                    <hr>
                    
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to List
                    </a>
                    
                    <a asp-action="Create" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-1"></i>Create New
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

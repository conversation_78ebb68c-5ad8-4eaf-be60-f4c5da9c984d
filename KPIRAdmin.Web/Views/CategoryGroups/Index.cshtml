@model IEnumerable<CategoryGroupListDto>
@{
    ViewData["Title"] = "Category Groups";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Category Groups</h1>
    <a asp-action="Create" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>Add New Category Group
    </a>
</div>

<!-- Alert Messages -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}
@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<!-- Search and Filter Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Search & Filter</h5>
    </div>
    <div class="card-body">
        <form method="get" asp-action="Index">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="@ViewBag.Search" placeholder="Search category groups...">
                </div>
                <div class="col-md-2">
                    <label for="language" class="form-label">Language</label>
                    <select class="form-select" id="language" name="language">
                        <option value="en" selected="@(ViewBag.Language == "en")">English</option>
                        <option value="fr" selected="@(ViewBag.Language == "fr")">French</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="activeOnly" class="form-label">Status</label>
                    <select class="form-select" id="activeOnly" name="activeOnly">
                        <option value="">All</option>
                        <option value="true" selected="@(ViewBag.ActiveOnly == true)">Active Only</option>
                        <option value="false" selected="@(ViewBag.ActiveOnly == false)">Inactive Only</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="kpirFlag" class="form-label">KPIR</label>
                    <select class="form-select" id="kpirFlag" name="kpirFlag">
                        <option value="">All</option>
                        <option value="true" selected="@(ViewBag.KpirFlag == true)">KPIR Only</option>
                        <option value="false" selected="@(ViewBag.KpirFlag == false)">Non-KPIR</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="hasLegacyId" class="form-label">Legacy ID</label>
                    <select class="form-select" id="hasLegacyId" name="hasLegacyId">
                        <option value="">All</option>
                        <option value="true" selected="@(ViewBag.HasLegacyId == true)">With Legacy ID</option>
                        <option value="false" selected="@(ViewBag.HasLegacyId == false)">Without Legacy ID</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Category Groups Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Category Groups List (@Model.Count() items)</h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleLanguage('en')">
                <i class="fas fa-language me-1"></i>EN
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleLanguage('fr')">
                <i class="fas fa-language me-1"></i>FR
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Legacy ID</th>
                            <th class="lang-en">Category Group (EN)</th>
                            <th class="lang-fr d-none">Category Group (FR)</th>
                            <th class="lang-en">Short Description (EN)</th>
                            <th class="lang-fr d-none">Short Description (FR)</th>
                            <th>Status</th>
                            <th>KPIR</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.CategoryGroupId</td>
                                <td>
                                    @if (item.LegacySystemId.HasValue)
                                    {
                                        <span class="badge bg-warning text-dark">@item.LegacySystemId</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td class="lang-en">@item.CategoryGroupEnglish</td>
                                <td class="lang-fr d-none">@item.CategoryGroupFrench</td>
                                <td class="lang-en">
                                    @if (!string.IsNullOrEmpty(item.ShortDescriptionEnglish))
                                    {
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="@item.ShortDescriptionEnglish">
                                            @item.ShortDescriptionEnglish
                                        </span>
                                    }
                                </td>
                                <td class="lang-fr d-none">
                                    @if (!string.IsNullOrEmpty(item.ShortDescriptionFrench))
                                    {
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="@item.ShortDescriptionFrench">
                                            @item.ShortDescriptionFrench
                                        </span>
                                    }
                                </td>
                                <td>
                                    @if (item.ActiveFlag)
                                    {
                                        <span class="badge bg-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning">Inactive</span>
                                    }
                                </td>
                                <td>
                                    @if (item.KpirFlag == true)
                                    {
                                        <span class="badge bg-info">KPIR</span>
                                    }
                                    else if (item.KpirFlag == false)
                                    {
                                        <span class="badge bg-secondary">Non-KPIR</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-light text-dark">Not Set</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.CategoryGroupId" class="btn btn-outline-primary btn-sm" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.CategoryGroupId" class="btn btn-outline-secondary btn-sm" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.CategoryGroupId" class="btn btn-outline-danger btn-sm" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No category groups found</h5>
                <p class="text-muted">Try adjusting your search criteria or create a new category group.</p>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Create First Category Group
                </a>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function toggleLanguage(lang) {
            if (lang === 'en') {
                $('.lang-en').removeClass('d-none');
                $('.lang-fr').addClass('d-none');
            } else {
                $('.lang-en').addClass('d-none');
                $('.lang-fr').removeClass('d-none');
            }
        }

        // Auto-submit form on filter change
        $('#activeOnly, #kpirFlag, #language, #hasLegacyId').change(function() {
            $(this).closest('form').submit();
        });
    </script>
}

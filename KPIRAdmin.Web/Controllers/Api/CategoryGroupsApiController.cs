using Microsoft.AspNetCore.Mvc;
using KPIRAdmin.Data.DTOs;
using KPIRAdmin.Services.Interfaces;

namespace KPIRAdmin.Web.Controllers.Api;

/// <summary>
/// API Controller for Category Groups with RESTful endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class CategoryGroupsApiController : ControllerBase
{
    private readonly ICategoryGroupService _categoryGroupService;
    private readonly ILogger<CategoryGroupsApiController> _logger;

    public CategoryGroupsApiController(ICategoryGroupService categoryGroupService, ILogger<CategoryGroupsApiController> logger)
    {
        _categoryGroupService = categoryGroupService ?? throw new ArgumentNullException(nameof(categoryGroupService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get all category groups with optional filtering
    /// </summary>
    /// <param name="search">Search term</param>
    /// <param name="language">Language for search (en/fr)</param>
    /// <param name="activeOnly">Filter by active status</param>
    /// <param name="kpirFlag">Filter by KPIR flag</param>
    /// <param name="hasLegacyId">Filter by legacy ID presence</param>
    /// <returns>List of category groups</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<CategoryGroupListDto>>> GetCategoryGroups(
        [FromQuery] string? search = null,
        [FromQuery] string language = "en",
        [FromQuery] bool? activeOnly = null,
        [FromQuery] bool? kpirFlag = null,
        [FromQuery] bool? hasLegacyId = null)
    {
        try
        {
            IEnumerable<CategoryGroupListDto> categoryGroups;

            if (!string.IsNullOrWhiteSpace(search))
            {
                categoryGroups = await _categoryGroupService.SearchCategoryGroupsAsync(search, language);
            }
            else if (activeOnly == true)
            {
                categoryGroups = await _categoryGroupService.GetActiveCategoryGroupsAsync();
            }
            else if (kpirFlag.HasValue)
            {
                categoryGroups = await _categoryGroupService.GetCategoryGroupsByKpirFlagAsync(kpirFlag.Value);
            }
            else
            {
                categoryGroups = await _categoryGroupService.GetAllCategoryGroupsAsync();
            }

            // Apply additional filters
            if (hasLegacyId.HasValue)
            {
                categoryGroups = hasLegacyId.Value 
                    ? categoryGroups.Where(cg => cg.LegacySystemId.HasValue)
                    : categoryGroups.Where(cg => !cg.LegacySystemId.HasValue);
            }

            return Ok(categoryGroups);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category groups via API");
            return StatusCode(500, new { error = "An error occurred while retrieving category groups" });
        }
    }

    /// <summary>
    /// Get a specific category group by ID
    /// </summary>
    /// <param name="id">Category group ID</param>
    /// <returns>Category group details</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<CategoryGroupDto>> GetCategoryGroup(int id)
    {
        try
        {
            var categoryGroup = await _categoryGroupService.GetCategoryGroupByIdAsync(id);
            if (categoryGroup == null)
            {
                return NotFound(new { error = $"Category group with ID {id} not found" });
            }

            return Ok(categoryGroup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category group {CategoryGroupId} via API", id);
            return StatusCode(500, new { error = "An error occurred while retrieving the category group" });
        }
    }

    /// <summary>
    /// Get a category group by legacy system ID
    /// </summary>
    /// <param name="legacyId">Legacy system ID</param>
    /// <returns>Category group details</returns>
    [HttpGet("legacy/{legacyId}")]
    public async Task<ActionResult<CategoryGroupDto>> GetCategoryGroupByLegacyId(int legacyId)
    {
        try
        {
            var categoryGroup = await _categoryGroupService.GetCategoryGroupByLegacyIdAsync(legacyId);
            if (categoryGroup == null)
            {
                return NotFound(new { error = $"Category group with legacy ID {legacyId} not found" });
            }

            return Ok(categoryGroup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category group by legacy ID {LegacyId} via API", legacyId);
            return StatusCode(500, new { error = "An error occurred while retrieving the category group" });
        }
    }

    /// <summary>
    /// Create a new category group
    /// </summary>
    /// <param name="categoryGroupForm">Category group data</param>
    /// <returns>Created category group</returns>
    [HttpPost]
    public async Task<ActionResult<CategoryGroupDto>> CreateCategoryGroup([FromBody] CategoryGroupFormDto categoryGroupForm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var createdCategoryGroup = await _categoryGroupService.CreateCategoryGroupAsync(categoryGroupForm);
            return CreatedAtAction(nameof(GetCategoryGroup), new { id = createdCategoryGroup.CategoryGroupId }, createdCategoryGroup);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating category group via API");
            return StatusCode(500, new { error = "An error occurred while creating the category group" });
        }
    }

    /// <summary>
    /// Update an existing category group
    /// </summary>
    /// <param name="id">Category group ID</param>
    /// <param name="categoryGroupForm">Updated category group data</param>
    /// <returns>Updated category group</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<CategoryGroupDto>> UpdateCategoryGroup(int id, [FromBody] CategoryGroupFormDto categoryGroupForm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var updatedCategoryGroup = await _categoryGroupService.UpdateCategoryGroupAsync(id, categoryGroupForm);
            return Ok(updatedCategoryGroup);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating category group {CategoryGroupId} via API", id);
            return StatusCode(500, new { error = "An error occurred while updating the category group" });
        }
    }

    /// <summary>
    /// Delete a category group
    /// </summary>
    /// <param name="id">Category group ID</param>
    /// <returns>Success status</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteCategoryGroup(int id)
    {
        try
        {
            var result = await _categoryGroupService.DeleteCategoryGroupAsync(id);
            if (!result)
            {
                return BadRequest(new { error = "Category group could not be deleted. It may have related records." });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category group {CategoryGroupId} via API", id);
            return StatusCode(500, new { error = "An error occurred while deleting the category group" });
        }
    }

    /// <summary>
    /// Soft delete (deactivate) a category group
    /// </summary>
    /// <param name="id">Category group ID</param>
    /// <returns>Success status</returns>
    [HttpPatch("{id}/deactivate")]
    public async Task<IActionResult> DeactivateCategoryGroup(int id)
    {
        try
        {
            var result = await _categoryGroupService.SoftDeleteCategoryGroupAsync(id);
            if (!result)
            {
                return BadRequest(new { error = "Category group could not be deactivated" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating category group {CategoryGroupId} via API", id);
            return StatusCode(500, new { error = "An error occurred while deactivating the category group" });
        }
    }

    /// <summary>
    /// Check if a category group name exists
    /// </summary>
    /// <param name="name">Category group name</param>
    /// <param name="language">Language (en/fr)</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>Existence status</returns>
    [HttpGet("check-name")]
    public async Task<ActionResult<object>> CheckNameExists(
        [FromQuery] string name,
        [FromQuery] string language = "en",
        [FromQuery] int? excludeId = null)
    {
        try
        {
            var exists = await _categoryGroupService.CategoryGroupNameExistsAsync(name, language, excludeId);
            return Ok(new { exists, name, language });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking category group name existence via API");
            return StatusCode(500, new { error = "An error occurred while checking name existence" });
        }
    }

    /// <summary>
    /// Check if a legacy system ID exists
    /// </summary>
    /// <param name="legacyId">Legacy system ID</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>Existence status</returns>
    [HttpGet("check-legacy-id")]
    public async Task<ActionResult<object>> CheckLegacyIdExists(
        [FromQuery] int legacyId,
        [FromQuery] int? excludeId = null)
    {
        try
        {
            var exists = await _categoryGroupService.LegacyIdExistsAsync(legacyId, excludeId);
            return Ok(new { exists, legacyId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking legacy ID existence via API");
            return StatusCode(500, new { error = "An error occurred while checking legacy ID existence" });
        }
    }

    /// <summary>
    /// Get category group statistics
    /// </summary>
    /// <returns>Category group statistics</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<object>> GetStatistics()
    {
        try
        {
            var totalCount = await _categoryGroupService.GetTotalCategoryGroupCountAsync();
            var activeCount = await _categoryGroupService.GetActiveCategoryGroupCountAsync();
            var kpirCount = await _categoryGroupService.GetKpirCategoryGroupCountAsync();
            var legacyIdCount = await _categoryGroupService.GetCategoryGroupsWithLegacyIdCountAsync();

            var stats = new
            {
                TotalCount = totalCount,
                ActiveCount = activeCount,
                InactiveCount = totalCount - activeCount,
                KpirCount = kpirCount,
                NonKpirCount = totalCount - kpirCount,
                WithLegacyIdCount = legacyIdCount,
                WithoutLegacyIdCount = totalCount - legacyIdCount
            };

            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category group statistics via API");
            return StatusCode(500, new { error = "An error occurred while retrieving statistics" });
        }
    }

    /// <summary>
    /// Bulk update active status for multiple category groups
    /// </summary>
    /// <param name="request">Bulk update request</param>
    /// <returns>Number of updated records</returns>
    [HttpPatch("bulk-active-status")]
    public async Task<ActionResult<object>> BulkUpdateActiveStatus([FromBody] BulkUpdateCategoryGroupActiveStatusRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var updatedCount = await _categoryGroupService.BulkUpdateActiveStatusAsync(request.CategoryGroupIds, request.ActiveStatus);
            return Ok(new { updatedCount });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating category group active status via API");
            return StatusCode(500, new { error = "An error occurred while updating category groups" });
        }
    }

    /// <summary>
    /// Bulk update KPIR flag for multiple category groups
    /// </summary>
    /// <param name="request">Bulk update request</param>
    /// <returns>Number of updated records</returns>
    [HttpPatch("bulk-kpir-flag")]
    public async Task<ActionResult<object>> BulkUpdateKpirFlag([FromBody] BulkUpdateCategoryGroupKpirFlagRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var updatedCount = await _categoryGroupService.BulkUpdateKpirFlagAsync(request.CategoryGroupIds, request.KpirFlag);
            return Ok(new { updatedCount });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating category group KPIR flag via API");
            return StatusCode(500, new { error = "An error occurred while updating category groups" });
        }
    }
}

/// <summary>
/// Request model for bulk active status update
/// </summary>
public class BulkUpdateCategoryGroupActiveStatusRequest
{
    public IEnumerable<int> CategoryGroupIds { get; set; } = new List<int>();
    public bool ActiveStatus { get; set; }
}

/// <summary>
/// Request model for bulk KPIR flag update
/// </summary>
public class BulkUpdateCategoryGroupKpirFlagRequest
{
    public IEnumerable<int> CategoryGroupIds { get; set; } = new List<int>();
    public bool? KpirFlag { get; set; }
}

using Microsoft.AspNetCore.Mvc;
using KPIRAdmin.Data.DTOs;
using KPIRAdmin.Services.Interfaces;

namespace KPIRAdmin.Web.Controllers.Api;

/// <summary>
/// API Controller for Categories with RESTful endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class CategoriesApiController : ControllerBase
{
    private readonly ICategoryService _categoryService;
    private readonly ILogger<CategoriesApiController> _logger;

    public CategoriesApiController(ICategoryService categoryService, ILogger<CategoriesApiController> logger)
    {
        _categoryService = categoryService ?? throw new ArgumentNullException(nameof(categoryService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get all categories with optional filtering
    /// </summary>
    /// <param name="search">Search term</param>
    /// <param name="language">Language for search (en/fr)</param>
    /// <param name="activeOnly">Filter by active status</param>
    /// <param name="kpirFlag">Filter by KPIR flag</param>
    /// <returns>List of categories</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<CategoryListDto>>> GetCategories(
        [FromQuery] string? search = null,
        [FromQuery] string language = "en",
        [FromQuery] bool? activeOnly = null,
        [FromQuery] bool? kpirFlag = null)
    {
        try
        {
            IEnumerable<CategoryListDto> categories;

            if (!string.IsNullOrWhiteSpace(search))
            {
                categories = await _categoryService.SearchCategoriesAsync(search, language);
            }
            else if (activeOnly == true)
            {
                categories = await _categoryService.GetActiveCategoriesAsync();
            }
            else if (kpirFlag.HasValue)
            {
                categories = await _categoryService.GetCategoriesByKpirFlagAsync(kpirFlag.Value);
            }
            else
            {
                categories = await _categoryService.GetAllCategoriesAsync();
            }

            return Ok(categories);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving categories via API");
            return StatusCode(500, new { error = "An error occurred while retrieving categories" });
        }
    }

    /// <summary>
    /// Get a specific category by ID
    /// </summary>
    /// <param name="id">Category ID</param>
    /// <returns>Category details</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<CategoryDto>> GetCategory(int id)
    {
        try
        {
            var category = await _categoryService.GetCategoryByIdAsync(id);
            if (category == null)
            {
                return NotFound(new { error = $"Category with ID {id} not found" });
            }

            return Ok(category);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category {CategoryId} via API", id);
            return StatusCode(500, new { error = "An error occurred while retrieving the category" });
        }
    }

    /// <summary>
    /// Create a new category
    /// </summary>
    /// <param name="categoryForm">Category data</param>
    /// <returns>Created category</returns>
    [HttpPost]
    public async Task<ActionResult<CategoryDto>> CreateCategory([FromBody] CategoryFormDto categoryForm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var createdCategory = await _categoryService.CreateCategoryAsync(categoryForm);
            return CreatedAtAction(nameof(GetCategory), new { id = createdCategory.CategoryId }, createdCategory);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating category via API");
            return StatusCode(500, new { error = "An error occurred while creating the category" });
        }
    }

    /// <summary>
    /// Update an existing category
    /// </summary>
    /// <param name="id">Category ID</param>
    /// <param name="categoryForm">Updated category data</param>
    /// <returns>Updated category</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<CategoryDto>> UpdateCategory(int id, [FromBody] CategoryFormDto categoryForm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var updatedCategory = await _categoryService.UpdateCategoryAsync(id, categoryForm);
            return Ok(updatedCategory);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating category {CategoryId} via API", id);
            return StatusCode(500, new { error = "An error occurred while updating the category" });
        }
    }

    /// <summary>
    /// Delete a category
    /// </summary>
    /// <param name="id">Category ID</param>
    /// <returns>Success status</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteCategory(int id)
    {
        try
        {
            var result = await _categoryService.DeleteCategoryAsync(id);
            if (!result)
            {
                return BadRequest(new { error = "Category could not be deleted. It may have related records." });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category {CategoryId} via API", id);
            return StatusCode(500, new { error = "An error occurred while deleting the category" });
        }
    }

    /// <summary>
    /// Soft delete (deactivate) a category
    /// </summary>
    /// <param name="id">Category ID</param>
    /// <returns>Success status</returns>
    [HttpPatch("{id}/deactivate")]
    public async Task<IActionResult> DeactivateCategory(int id)
    {
        try
        {
            var result = await _categoryService.SoftDeleteCategoryAsync(id);
            if (!result)
            {
                return BadRequest(new { error = "Category could not be deactivated" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating category {CategoryId} via API", id);
            return StatusCode(500, new { error = "An error occurred while deactivating the category" });
        }
    }

    /// <summary>
    /// Check if a category name exists
    /// </summary>
    /// <param name="name">Category name</param>
    /// <param name="language">Language (en/fr)</param>
    /// <param name="excludeId">ID to exclude from check</param>
    /// <returns>Existence status</returns>
    [HttpGet("check-name")]
    public async Task<ActionResult<object>> CheckNameExists(
        [FromQuery] string name,
        [FromQuery] string language = "en",
        [FromQuery] int? excludeId = null)
    {
        try
        {
            var exists = await _categoryService.CategoryNameExistsAsync(name, language, excludeId);
            return Ok(new { exists, name, language });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking category name existence via API");
            return StatusCode(500, new { error = "An error occurred while checking name existence" });
        }
    }

    /// <summary>
    /// Get category statistics
    /// </summary>
    /// <returns>Category statistics</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<object>> GetStatistics()
    {
        try
        {
            var totalCount = await _categoryService.GetTotalCategoryCountAsync();
            var activeCount = await _categoryService.GetActiveCategoryCountAsync();
            var kpirCount = await _categoryService.GetKpirCategoryCountAsync();

            var stats = new
            {
                TotalCount = totalCount,
                ActiveCount = activeCount,
                InactiveCount = totalCount - activeCount,
                KpirCount = kpirCount,
                NonKpirCount = totalCount - kpirCount
            };

            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category statistics via API");
            return StatusCode(500, new { error = "An error occurred while retrieving statistics" });
        }
    }

    /// <summary>
    /// Bulk update active status for multiple categories
    /// </summary>
    /// <param name="request">Bulk update request</param>
    /// <returns>Number of updated records</returns>
    [HttpPatch("bulk-active-status")]
    public async Task<ActionResult<object>> BulkUpdateActiveStatus([FromBody] BulkUpdateActiveStatusRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var updatedCount = await _categoryService.BulkUpdateActiveStatusAsync(request.CategoryIds, request.ActiveStatus);
            return Ok(new { updatedCount });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating category active status via API");
            return StatusCode(500, new { error = "An error occurred while updating categories" });
        }
    }

    /// <summary>
    /// Bulk update KPIR flag for multiple categories
    /// </summary>
    /// <param name="request">Bulk update request</param>
    /// <returns>Number of updated records</returns>
    [HttpPatch("bulk-kpir-flag")]
    public async Task<ActionResult<object>> BulkUpdateKpirFlag([FromBody] BulkUpdateKpirFlagRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var updatedCount = await _categoryService.BulkUpdateKpirFlagAsync(request.CategoryIds, request.KpirFlag);
            return Ok(new { updatedCount });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating category KPIR flag via API");
            return StatusCode(500, new { error = "An error occurred while updating categories" });
        }
    }
}

/// <summary>
/// Request model for bulk active status update
/// </summary>
public class BulkUpdateActiveStatusRequest
{
    public IEnumerable<int> CategoryIds { get; set; } = new List<int>();
    public bool ActiveStatus { get; set; }
}

/// <summary>
/// Request model for bulk KPIR flag update
/// </summary>
public class BulkUpdateKpirFlagRequest
{
    public IEnumerable<int> CategoryIds { get; set; } = new List<int>();
    public bool? KpirFlag { get; set; }
}

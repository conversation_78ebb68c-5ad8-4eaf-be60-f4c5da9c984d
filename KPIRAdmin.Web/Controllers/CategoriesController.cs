using Microsoft.AspNetCore.Mvc;
using KPIRAdmin.Data.DTOs;
using KPIRAdmin.Services.Interfaces;

namespace KPIRAdmin.Web.Controllers;

/// <summary>
/// Controller for managing Categories with full CRUD operations
/// </summary>
public class CategoriesController : Controller
{
    private readonly ICategoryService _categoryService;
    private readonly ILogger<CategoriesController> _logger;

    public CategoriesController(ICategoryService categoryService, ILogger<CategoriesController> logger)
    {
        _categoryService = categoryService ?? throw new ArgumentNullException(nameof(categoryService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    // GET: Categories
    [HttpGet]
    public async Task<IActionResult> Index(string? search, string language = "en", bool? activeOnly = null, bool? kpirFlag = null)
    {
        try
        {
            IEnumerable<CategoryListDto> categories;

            if (!string.IsNullOrWhiteSpace(search))
            {
                categories = await _categoryService.SearchCategoriesAsync(search, language);
            }
            else if (activeOnly == true)
            {
                categories = await _categoryService.GetActiveCategoriesAsync();
            }
            else if (kpirFlag.HasValue)
            {
                categories = await _categoryService.GetCategoriesByKpirFlagAsync(kpirFlag.Value);
            }
            else
            {
                categories = await _categoryService.GetAllCategoriesAsync();
            }

            // Pass filter parameters to view for maintaining state
            ViewBag.Search = search;
            ViewBag.Language = language;
            ViewBag.ActiveOnly = activeOnly;
            ViewBag.KpirFlag = kpirFlag;

            return View(categories);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving categories");
            TempData["ErrorMessage"] = "An error occurred while retrieving categories.";
            return View(new List<CategoryListDto>());
        }
    }

    // GET: Categories/Details/5
    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var category = await _categoryService.GetCategoryByIdAsync(id);
            if (category == null)
            {
                return NotFound();
            }

            return View(category);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category details for ID {CategoryId}", id);
            TempData["ErrorMessage"] = "An error occurred while retrieving category details.";
            return RedirectToAction(nameof(Index));
        }
    }

    // GET: Categories/Create
    [HttpGet]
    public IActionResult Create()
    {
        var model = new CategoryFormDto();
        return View(model);
    }

    // POST: Categories/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CategoryFormDto categoryForm)
    {
        if (!ModelState.IsValid)
        {
            return View(categoryForm);
        }

        try
        {
            var createdCategory = await _categoryService.CreateCategoryAsync(categoryForm);
            TempData["SuccessMessage"] = "Category created successfully.";
            return RedirectToAction(nameof(Details), new { id = createdCategory.CategoryId });
        }
        catch (ArgumentException ex)
        {
            ModelState.AddModelError("", ex.Message);
            return View(categoryForm);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating category");
            ModelState.AddModelError("", "An error occurred while creating the category.");
            return View(categoryForm);
        }
    }

    // GET: Categories/Edit/5
    [HttpGet]
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            var category = await _categoryService.GetCategoryByIdAsync(id);
            if (category == null)
            {
                return NotFound();
            }

            var categoryForm = new CategoryFormDto
            {
                CategoryId = category.CategoryId,
                CategoryEnglish = category.CategoryEnglish,
                CategoryFrench = category.CategoryFrench,
                DescriptionEnglish = category.DescriptionEnglish,
                DescriptionFrench = category.DescriptionFrench,
                ShortDescriptionEnglish = category.ShortDescriptionEnglish,
                ShortDescriptionFrench = category.ShortDescriptionFrench,
                SortOrderEnglish = category.SortOrderEnglish,
                SortOrderFrench = category.SortOrderFrench,
                KpirFlag = category.KpirFlag,
                ActiveFlag = category.ActiveFlag
            };

            return View(categoryForm);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category for edit with ID {CategoryId}", id);
            TempData["ErrorMessage"] = "An error occurred while retrieving the category for editing.";
            return RedirectToAction(nameof(Index));
        }
    }

    // POST: Categories/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, CategoryFormDto categoryForm)
    {
        if (id != categoryForm.CategoryId)
        {
            return BadRequest();
        }

        if (!ModelState.IsValid)
        {
            return View(categoryForm);
        }

        try
        {
            var updatedCategory = await _categoryService.UpdateCategoryAsync(id, categoryForm);
            TempData["SuccessMessage"] = "Category updated successfully.";
            return RedirectToAction(nameof(Details), new { id = updatedCategory.CategoryId });
        }
        catch (ArgumentException ex)
        {
            ModelState.AddModelError("", ex.Message);
            return View(categoryForm);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating category with ID {CategoryId}", id);
            ModelState.AddModelError("", "An error occurred while updating the category.");
            return View(categoryForm);
        }
    }

    // GET: Categories/Delete/5
    [HttpGet]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var category = await _categoryService.GetCategoryByIdAsync(id);
            if (category == null)
            {
                return NotFound();
            }

            return View(category);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category for delete with ID {CategoryId}", id);
            TempData["ErrorMessage"] = "An error occurred while retrieving the category for deletion.";
            return RedirectToAction(nameof(Index));
        }
    }

    // POST: Categories/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            var result = await _categoryService.DeleteCategoryAsync(id);
            if (result)
            {
                TempData["SuccessMessage"] = "Category deleted successfully.";
            }
            else
            {
                TempData["ErrorMessage"] = "Category could not be deleted. It may have related records.";
            }

            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category with ID {CategoryId}", id);
            TempData["ErrorMessage"] = "An error occurred while deleting the category.";
            return RedirectToAction(nameof(Index));
        }
    }

    // POST: Categories/SoftDelete/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SoftDelete(int id)
    {
        try
        {
            var result = await _categoryService.SoftDeleteCategoryAsync(id);
            if (result)
            {
                TempData["SuccessMessage"] = "Category deactivated successfully.";
            }
            else
            {
                TempData["ErrorMessage"] = "Category could not be deactivated.";
            }

            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error soft deleting category with ID {CategoryId}", id);
            TempData["ErrorMessage"] = "An error occurred while deactivating the category.";
            return RedirectToAction(nameof(Index));
        }
    }

    // GET: Categories/CheckNameExists
    [HttpGet]
    public async Task<IActionResult> CheckNameExists(string name, string language = "en", int? excludeId = null)
    {
        try
        {
            var exists = await _categoryService.CategoryNameExistsAsync(name, language, excludeId);
            return Json(new { exists });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if category name exists");
            return Json(new { exists = false });
        }
    }

    // GET: Categories/Statistics
    [HttpGet]
    public async Task<IActionResult> Statistics()
    {
        try
        {
            var totalCount = await _categoryService.GetTotalCategoryCountAsync();
            var activeCount = await _categoryService.GetActiveCategoryCountAsync();
            var kpirCount = await _categoryService.GetKpirCategoryCountAsync();

            var stats = new
            {
                TotalCount = totalCount,
                ActiveCount = activeCount,
                InactiveCount = totalCount - activeCount,
                KpirCount = kpirCount,
                NonKpirCount = totalCount - kpirCount
            };

            return Json(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category statistics");
            return Json(new { error = "Unable to retrieve statistics" });
        }
    }
}

using Microsoft.AspNetCore.Mvc;
using KPIRAdmin.Data.DTOs;
using KPIRAdmin.Services.Interfaces;

namespace KPIRAdmin.Web.Controllers;

/// <summary>
/// Controller for managing Category Groups with full CRUD operations
/// </summary>
public class CategoryGroupsController : Controller
{
    private readonly ICategoryGroupService _categoryGroupService;
    private readonly ILogger<CategoryGroupsController> _logger;

    public CategoryGroupsController(ICategoryGroupService categoryGroupService, ILogger<CategoryGroupsController> logger)
    {
        _categoryGroupService = categoryGroupService ?? throw new ArgumentNullException(nameof(categoryGroupService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    // GET: CategoryGroups
    [HttpGet]
    public async Task<IActionResult> Index(string? search, string language = "en", bool? activeOnly = null, bool? kpirFlag = null, bool? hasLegacyId = null)
    {
        try
        {
            IEnumerable<CategoryGroupListDto> categoryGroups;

            if (!string.IsNullOrWhiteSpace(search))
            {
                categoryGroups = await _categoryGroupService.SearchCategoryGroupsAsync(search, language);
            }
            else if (activeOnly == true)
            {
                categoryGroups = await _categoryGroupService.GetActiveCategoryGroupsAsync();
            }
            else if (kpirFlag.HasValue)
            {
                categoryGroups = await _categoryGroupService.GetCategoryGroupsByKpirFlagAsync(kpirFlag.Value);
            }
            else
            {
                categoryGroups = await _categoryGroupService.GetAllCategoryGroupsAsync();
            }

            // Apply additional filters
            if (hasLegacyId.HasValue)
            {
                categoryGroups = hasLegacyId.Value 
                    ? categoryGroups.Where(cg => cg.LegacySystemId.HasValue)
                    : categoryGroups.Where(cg => !cg.LegacySystemId.HasValue);
            }

            // Pass filter parameters to view for maintaining state
            ViewBag.Search = search;
            ViewBag.Language = language;
            ViewBag.ActiveOnly = activeOnly;
            ViewBag.KpirFlag = kpirFlag;
            ViewBag.HasLegacyId = hasLegacyId;

            return View(categoryGroups);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category groups");
            TempData["ErrorMessage"] = "An error occurred while retrieving category groups.";
            return View(new List<CategoryGroupListDto>());
        }
    }

    // GET: CategoryGroups/Details/5
    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var categoryGroup = await _categoryGroupService.GetCategoryGroupByIdAsync(id);
            if (categoryGroup == null)
            {
                return NotFound();
            }

            return View(categoryGroup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category group details for ID {CategoryGroupId}", id);
            TempData["ErrorMessage"] = "An error occurred while retrieving category group details.";
            return RedirectToAction(nameof(Index));
        }
    }

    // GET: CategoryGroups/Create
    [HttpGet]
    public IActionResult Create()
    {
        var model = new CategoryGroupFormDto();
        return View(model);
    }

    // POST: CategoryGroups/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CategoryGroupFormDto categoryGroupForm)
    {
        if (!ModelState.IsValid)
        {
            return View(categoryGroupForm);
        }

        try
        {
            var createdCategoryGroup = await _categoryGroupService.CreateCategoryGroupAsync(categoryGroupForm);
            TempData["SuccessMessage"] = "Category group created successfully.";
            return RedirectToAction(nameof(Details), new { id = createdCategoryGroup.CategoryGroupId });
        }
        catch (ArgumentException ex)
        {
            ModelState.AddModelError("", ex.Message);
            return View(categoryGroupForm);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating category group");
            ModelState.AddModelError("", "An error occurred while creating the category group.");
            return View(categoryGroupForm);
        }
    }

    // GET: CategoryGroups/Edit/5
    [HttpGet]
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            var categoryGroup = await _categoryGroupService.GetCategoryGroupByIdAsync(id);
            if (categoryGroup == null)
            {
                return NotFound();
            }

            var categoryGroupForm = new CategoryGroupFormDto
            {
                CategoryGroupId = categoryGroup.CategoryGroupId,
                LegacySystemId = categoryGroup.LegacySystemId,
                CategoryGroupEnglish = categoryGroup.CategoryGroupEnglish,
                CategoryGroupFrench = categoryGroup.CategoryGroupFrench,
                DescriptionEnglish = categoryGroup.DescriptionEnglish,
                DescriptionFrench = categoryGroup.DescriptionFrench,
                ShortDescriptionEnglish = categoryGroup.ShortDescriptionEnglish,
                ShortDescriptionFrench = categoryGroup.ShortDescriptionFrench,
                SortOrderEnglish = categoryGroup.SortOrderEnglish,
                SortOrderFrench = categoryGroup.SortOrderFrench,
                KpirFlag = categoryGroup.KpirFlag,
                ActiveFlag = categoryGroup.ActiveFlag
            };

            return View(categoryGroupForm);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category group for edit with ID {CategoryGroupId}", id);
            TempData["ErrorMessage"] = "An error occurred while retrieving the category group for editing.";
            return RedirectToAction(nameof(Index));
        }
    }

    // POST: CategoryGroups/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, CategoryGroupFormDto categoryGroupForm)
    {
        if (id != categoryGroupForm.CategoryGroupId)
        {
            return BadRequest();
        }

        if (!ModelState.IsValid)
        {
            return View(categoryGroupForm);
        }

        try
        {
            var updatedCategoryGroup = await _categoryGroupService.UpdateCategoryGroupAsync(id, categoryGroupForm);
            TempData["SuccessMessage"] = "Category group updated successfully.";
            return RedirectToAction(nameof(Details), new { id = updatedCategoryGroup.CategoryGroupId });
        }
        catch (ArgumentException ex)
        {
            ModelState.AddModelError("", ex.Message);
            return View(categoryGroupForm);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating category group with ID {CategoryGroupId}", id);
            ModelState.AddModelError("", "An error occurred while updating the category group.");
            return View(categoryGroupForm);
        }
    }

    // GET: CategoryGroups/Delete/5
    [HttpGet]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var categoryGroup = await _categoryGroupService.GetCategoryGroupByIdAsync(id);
            if (categoryGroup == null)
            {
                return NotFound();
            }

            return View(categoryGroup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category group for delete with ID {CategoryGroupId}", id);
            TempData["ErrorMessage"] = "An error occurred while retrieving the category group for deletion.";
            return RedirectToAction(nameof(Index));
        }
    }

    // POST: CategoryGroups/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            var result = await _categoryGroupService.DeleteCategoryGroupAsync(id);
            if (result)
            {
                TempData["SuccessMessage"] = "Category group deleted successfully.";
            }
            else
            {
                TempData["ErrorMessage"] = "Category group could not be deleted. It may have related records.";
            }

            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category group with ID {CategoryGroupId}", id);
            TempData["ErrorMessage"] = "An error occurred while deleting the category group.";
            return RedirectToAction(nameof(Index));
        }
    }

    // POST: CategoryGroups/SoftDelete/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SoftDelete(int id)
    {
        try
        {
            var result = await _categoryGroupService.SoftDeleteCategoryGroupAsync(id);
            if (result)
            {
                TempData["SuccessMessage"] = "Category group deactivated successfully.";
            }
            else
            {
                TempData["ErrorMessage"] = "Category group could not be deactivated.";
            }

            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error soft deleting category group with ID {CategoryGroupId}", id);
            TempData["ErrorMessage"] = "An error occurred while deactivating the category group.";
            return RedirectToAction(nameof(Index));
        }
    }

    // GET: CategoryGroups/ByLegacyId/5
    [HttpGet]
    public async Task<IActionResult> ByLegacyId(int legacyId)
    {
        try
        {
            var categoryGroup = await _categoryGroupService.GetCategoryGroupByLegacyIdAsync(legacyId);
            if (categoryGroup == null)
            {
                return NotFound();
            }

            return RedirectToAction(nameof(Details), new { id = categoryGroup.CategoryGroupId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category group by legacy ID {LegacyId}", legacyId);
            TempData["ErrorMessage"] = "An error occurred while retrieving the category group.";
            return RedirectToAction(nameof(Index));
        }
    }

    // GET: CategoryGroups/CheckNameExists
    [HttpGet]
    public async Task<IActionResult> CheckNameExists(string name, string language = "en", int? excludeId = null)
    {
        try
        {
            var exists = await _categoryGroupService.CategoryGroupNameExistsAsync(name, language, excludeId);
            return Json(new { exists });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if category group name exists");
            return Json(new { exists = false });
        }
    }

    // GET: CategoryGroups/CheckLegacyIdExists
    [HttpGet]
    public async Task<IActionResult> CheckLegacyIdExists(int legacyId, int? excludeId = null)
    {
        try
        {
            var exists = await _categoryGroupService.LegacyIdExistsAsync(legacyId, excludeId);
            return Json(new { exists });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if legacy ID exists");
            return Json(new { exists = false });
        }
    }

    // GET: CategoryGroups/Statistics
    [HttpGet]
    public async Task<IActionResult> Statistics()
    {
        try
        {
            var totalCount = await _categoryGroupService.GetTotalCategoryGroupCountAsync();
            var activeCount = await _categoryGroupService.GetActiveCategoryGroupCountAsync();
            var kpirCount = await _categoryGroupService.GetKpirCategoryGroupCountAsync();
            var legacyIdCount = await _categoryGroupService.GetCategoryGroupsWithLegacyIdCountAsync();

            var stats = new
            {
                TotalCount = totalCount,
                ActiveCount = activeCount,
                InactiveCount = totalCount - activeCount,
                KpirCount = kpirCount,
                NonKpirCount = totalCount - kpirCount,
                WithLegacyIdCount = legacyIdCount,
                WithoutLegacyIdCount = totalCount - legacyIdCount
            };

            return Json(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category group statistics");
            return Json(new { error = "Unable to retrieve statistics" });
        }
    }
}

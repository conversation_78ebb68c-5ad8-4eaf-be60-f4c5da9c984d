using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using KPIRAdmin.Web.Models;
using KPIRAdmin.Services.Interfaces;

namespace KPIRAdmin.Web.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ICategoryService _categoryService;
    private readonly ICategoryGroupService _categoryGroupService;

    public HomeController(
        ILogger<HomeController> logger,
        ICategoryService categoryService,
        ICategoryGroupService categoryGroupService)
    {
        _logger = logger;
        _categoryService = categoryService;
        _categoryGroupService = categoryGroupService;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            var viewModel = new DashboardViewModel();

            // Get category statistics
            var totalCategories = await _categoryService.GetTotalCategoryCountAsync();
            var activeCategories = await _categoryService.GetActiveCategoryCountAsync();
            var kpirCategories = await _categoryService.GetKpirCategoryCountAsync();

            viewModel.CategoryStats = new CategoryStatistics
            {
                TotalCount = totalCategories,
                ActiveCount = activeCategories,
                InactiveCount = totalCategories - activeCategories,
                KpirCount = kpirCategories,
                NonKpirCount = totalCategories - kpirCategories
            };

            // Get category group statistics
            var totalCategoryGroups = await _categoryGroupService.GetTotalCategoryGroupCountAsync();
            var activeCategoryGroups = await _categoryGroupService.GetActiveCategoryGroupCountAsync();
            var kpirCategoryGroups = await _categoryGroupService.GetKpirCategoryGroupCountAsync();
            var legacyIdCategoryGroups = await _categoryGroupService.GetCategoryGroupsWithLegacyIdCountAsync();

            viewModel.CategoryGroupStats = new CategoryGroupStatistics
            {
                TotalCount = totalCategoryGroups,
                ActiveCount = activeCategoryGroups,
                InactiveCount = totalCategoryGroups - activeCategoryGroups,
                KpirCount = kpirCategoryGroups,
                NonKpirCount = totalCategoryGroups - kpirCategoryGroups,
                WithLegacyIdCount = legacyIdCategoryGroups,
                WithoutLegacyIdCount = totalCategoryGroups - legacyIdCategoryGroups
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard");
            // Return empty view model on error
            return View(new DashboardViewModel());
        }
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}

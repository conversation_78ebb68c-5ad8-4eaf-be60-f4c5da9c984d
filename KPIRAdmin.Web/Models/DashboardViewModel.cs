namespace KPIRAdmin.Web.Models;

/// <summary>
/// View model for the dashboard/home page
/// </summary>
public class DashboardViewModel
{
    public CategoryStatistics CategoryStats { get; set; } = new();
    public CategoryGroupStatistics CategoryGroupStats { get; set; } = new();
}

/// <summary>
/// Statistics for categories
/// </summary>
public class CategoryStatistics
{
    public int TotalCount { get; set; }
    public int ActiveCount { get; set; }
    public int InactiveCount { get; set; }
    public int KpirCount { get; set; }
    public int NonKpirCount { get; set; }
}

/// <summary>
/// Statistics for category groups
/// </summary>
public class CategoryGroupStatistics
{
    public int TotalCount { get; set; }
    public int ActiveCount { get; set; }
    public int InactiveCount { get; set; }
    public int KpirCount { get; set; }
    public int NonKpirCount { get; set; }
    public int WithLegacyIdCount { get; set; }
    public int WithoutLegacyIdCount { get; set; }
}

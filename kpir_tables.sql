USE [KPIR]
GO
/****** Object:  Table [dbo].[categories]    Script Date: 2025-07-21 1:20:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[categories](
	[category_id] [int] IDENTITY(1,1) NOT NULL,
	[category_e] [varchar](50) NULL,
	[category_f] [varchar](50) NULL,
	[desc_e] [varchar](5000) NULL,
	[desc_f] [varchar](5000) NULL,
	[short_desc_e] [varchar](500) NULL,
	[short_desc_f] [varchar](500) NULL,
	[sort_order_e] [int] NULL,
	[sort_order_f] [int] NULL,
	[kpir_flag] [bit] NULL,
	[active_flag] [bit] NOT NULL,
 CONSTRAINT [PK_categories] PRIMARY KEY CLUSTERED 
(
	[category_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[category_groups]    Script Date: 2025-07-21 1:20:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[category_groups](
	[category_group_id] [int] IDENTITY(1,1) NOT NULL,
	[legacy_system_id] [int] NULL,
	[category_group_e] [varchar](50) NULL,
	[category_group_f] [varchar](50) NULL,
	[desc_e] [varchar](1000) NULL,
	[desc_f] [varchar](1000) NULL,
	[short_desc_e] [varchar](500) NULL,
	[short_desc_f] [varchar](500) NULL,
	[sort_order_e] [int] NULL,
	[sort_order_f] [int] NULL,
	[kpir_flag] [bit] NULL,
	[active_flag] [bit] NOT NULL,
 CONSTRAINT [PK_category_groups] PRIMARY KEY CLUSTERED 
(
	[category_group_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
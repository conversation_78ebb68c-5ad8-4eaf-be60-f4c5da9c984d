using Microsoft.EntityFrameworkCore;
using KPIRAdmin.Data.Entities;

namespace KPIRAdmin.Data;

/// <summary>
/// Entity Framework DbContext for KPIR Admin application
/// </summary>
public class KpirDbContext : DbContext
{
    public KpirDbContext(DbContextOptions<KpirDbContext> options) : base(options)
    {
    }

    // DbSets for entities
    public DbSet<Category> Categories { get; set; }
    public DbSet<CategoryGroup> CategoryGroups { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Category entity
        modelBuilder.Entity<Category>(entity =>
        {
            entity.HasKey(e => e.CategoryId);
            entity.Property(e => e.CategoryId).ValueGeneratedOnAdd();
            
            // Configure string lengths to match database schema
            entity.Property(e => e.CategoryEnglish).HasMaxLength(50);
            entity.Property(e => e.CategoryFrench).HasMaxLength(50);
            entity.Property(e => e.DescriptionEnglish).HasMaxLength(5000);
            entity.Property(e => e.DescriptionFrench).HasMaxLength(5000);
            entity.Property(e => e.ShortDescriptionEnglish).HasMaxLength(500);
            entity.Property(e => e.ShortDescriptionFrench).HasMaxLength(500);
            
            // Configure required fields
            entity.Property(e => e.ActiveFlag).IsRequired();
            
            // Configure nullable fields
            entity.Property(e => e.CategoryEnglish).IsRequired(false);
            entity.Property(e => e.CategoryFrench).IsRequired(false);
            entity.Property(e => e.DescriptionEnglish).IsRequired(false);
            entity.Property(e => e.DescriptionFrench).IsRequired(false);
            entity.Property(e => e.ShortDescriptionEnglish).IsRequired(false);
            entity.Property(e => e.ShortDescriptionFrench).IsRequired(false);
            entity.Property(e => e.SortOrderEnglish).IsRequired(false);
            entity.Property(e => e.SortOrderFrench).IsRequired(false);
            entity.Property(e => e.KpirFlag).IsRequired(false);
        });

        // Configure CategoryGroup entity
        modelBuilder.Entity<CategoryGroup>(entity =>
        {
            entity.HasKey(e => e.CategoryGroupId);
            entity.Property(e => e.CategoryGroupId).ValueGeneratedOnAdd();
            
            // Configure string lengths to match database schema
            entity.Property(e => e.CategoryGroupEnglish).HasMaxLength(50);
            entity.Property(e => e.CategoryGroupFrench).HasMaxLength(50);
            entity.Property(e => e.DescriptionEnglish).HasMaxLength(1000);
            entity.Property(e => e.DescriptionFrench).HasMaxLength(1000);
            entity.Property(e => e.ShortDescriptionEnglish).HasMaxLength(500);
            entity.Property(e => e.ShortDescriptionFrench).HasMaxLength(500);
            
            // Configure required fields
            entity.Property(e => e.ActiveFlag).IsRequired();
            
            // Configure nullable fields
            entity.Property(e => e.LegacySystemId).IsRequired(false);
            entity.Property(e => e.CategoryGroupEnglish).IsRequired(false);
            entity.Property(e => e.CategoryGroupFrench).IsRequired(false);
            entity.Property(e => e.DescriptionEnglish).IsRequired(false);
            entity.Property(e => e.DescriptionFrench).IsRequired(false);
            entity.Property(e => e.ShortDescriptionEnglish).IsRequired(false);
            entity.Property(e => e.ShortDescriptionFrench).IsRequired(false);
            entity.Property(e => e.SortOrderEnglish).IsRequired(false);
            entity.Property(e => e.SortOrderFrench).IsRequired(false);
            entity.Property(e => e.KpirFlag).IsRequired(false);
        });

        // Add indexes for performance
        modelBuilder.Entity<Category>()
            .HasIndex(e => e.ActiveFlag)
            .HasDatabaseName("IX_Categories_ActiveFlag");

        modelBuilder.Entity<CategoryGroup>()
            .HasIndex(e => e.ActiveFlag)
            .HasDatabaseName("IX_CategoryGroups_ActiveFlag");

        modelBuilder.Entity<CategoryGroup>()
            .HasIndex(e => e.LegacySystemId)
            .HasDatabaseName("IX_CategoryGroups_LegacySystemId");
    }
}

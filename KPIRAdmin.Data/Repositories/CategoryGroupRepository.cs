using Microsoft.EntityFrameworkCore;
using KPIRAdmin.Data.Entities;

namespace KPIRAdmin.Data.Repositories;

/// <summary>
/// Repository implementation for CategoryGroup entity
/// </summary>
public class CategoryGroupRepository : Repository<CategoryGroup>, ICategoryGroupRepository
{
    public CategoryGroupRepository(KpirDbContext context) : base(context)
    {
    }

    // Override GetByIdAsync to use the correct key type
    public new async Task<CategoryGroup?> GetByIdAsync(int id)
    {
        return await _dbSet.FirstOrDefaultAsync(cg => cg.CategoryGroupId == id);
    }

    // CategoryGroup-specific operations
    public async Task<IEnumerable<CategoryGroup>> GetActiveCategoryGroupsAsync()
    {
        return await _dbSet
            .Where(cg => cg.ActiveFlag)
            .OrderBy(cg => cg.SortOrderEnglish ?? int.MaxValue)
            .ThenBy(cg => cg.CategoryGroupEnglish)
            .ToListAsync();
    }

    public async Task<IEnumerable<CategoryGroup>> GetCategoryGroupsByKpirFlagAsync(bool kpirFlag)
    {
        return await _dbSet
            .Where(cg => cg.KpirFlag == kpirFlag)
            .OrderBy(cg => cg.SortOrderEnglish ?? int.MaxValue)
            .ThenBy(cg => cg.CategoryGroupEnglish)
            .ToListAsync();
    }

    public async Task<IEnumerable<CategoryGroup>> SearchCategoryGroupsAsync(string searchTerm, string language = "en")
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return await GetActiveCategoryGroupsAsync();
        }

        var query = _dbSet.AsQueryable();

        if (language.ToLower() == "fr")
        {
            query = query.Where(cg => 
                (cg.CategoryGroupFrench != null && cg.CategoryGroupFrench.Contains(searchTerm)) ||
                (cg.DescriptionFrench != null && cg.DescriptionFrench.Contains(searchTerm)) ||
                (cg.ShortDescriptionFrench != null && cg.ShortDescriptionFrench.Contains(searchTerm)));
        }
        else
        {
            query = query.Where(cg => 
                (cg.CategoryGroupEnglish != null && cg.CategoryGroupEnglish.Contains(searchTerm)) ||
                (cg.DescriptionEnglish != null && cg.DescriptionEnglish.Contains(searchTerm)) ||
                (cg.ShortDescriptionEnglish != null && cg.ShortDescriptionEnglish.Contains(searchTerm)));
        }

        return await query
            .OrderBy(cg => language.ToLower() == "fr" ? cg.SortOrderFrench ?? int.MaxValue : cg.SortOrderEnglish ?? int.MaxValue)
            .ThenBy(cg => language.ToLower() == "fr" ? cg.CategoryGroupFrench : cg.CategoryGroupEnglish)
            .ToListAsync();
    }

    public async Task<IEnumerable<CategoryGroup>> GetCategoryGroupsSortedAsync(string language = "en", bool ascending = true)
    {
        var query = _dbSet.AsQueryable();

        if (language.ToLower() == "fr")
        {
            query = ascending 
                ? query.OrderBy(cg => cg.SortOrderFrench ?? int.MaxValue).ThenBy(cg => cg.CategoryGroupFrench)
                : query.OrderByDescending(cg => cg.SortOrderFrench ?? int.MinValue).ThenByDescending(cg => cg.CategoryGroupFrench);
        }
        else
        {
            query = ascending 
                ? query.OrderBy(cg => cg.SortOrderEnglish ?? int.MaxValue).ThenBy(cg => cg.CategoryGroupEnglish)
                : query.OrderByDescending(cg => cg.SortOrderEnglish ?? int.MinValue).ThenByDescending(cg => cg.CategoryGroupEnglish);
        }

        return await query.ToListAsync();
    }

    // Legacy system operations
    public async Task<CategoryGroup?> GetCategoryGroupByLegacyIdAsync(int legacySystemId)
    {
        return await _dbSet.FirstOrDefaultAsync(cg => cg.LegacySystemId == legacySystemId);
    }

    public async Task<IEnumerable<CategoryGroup>> GetCategoryGroupsByLegacyIdsAsync(IEnumerable<int> legacySystemIds)
    {
        return await _dbSet
            .Where(cg => cg.LegacySystemId.HasValue && legacySystemIds.Contains(cg.LegacySystemId.Value))
            .ToListAsync();
    }

    public async Task<bool> LegacyIdExistsAsync(int legacySystemId, int? excludeId = null)
    {
        var query = _dbSet.Where(cg => cg.LegacySystemId == legacySystemId);

        if (excludeId.HasValue)
        {
            query = query.Where(cg => cg.CategoryGroupId != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    // Bilingual operations
    public async Task<CategoryGroup?> GetCategoryGroupByNameAsync(string name, string language = "en")
    {
        if (language.ToLower() == "fr")
        {
            return await _dbSet.FirstOrDefaultAsync(cg => cg.CategoryGroupFrench == name);
        }
        else
        {
            return await _dbSet.FirstOrDefaultAsync(cg => cg.CategoryGroupEnglish == name);
        }
    }

    public async Task<bool> CategoryGroupNameExistsAsync(string name, string language = "en", int? excludeId = null)
    {
        var query = _dbSet.AsQueryable();

        if (excludeId.HasValue)
        {
            query = query.Where(cg => cg.CategoryGroupId != excludeId.Value);
        }

        if (language.ToLower() == "fr")
        {
            return await query.AnyAsync(cg => cg.CategoryGroupFrench == name);
        }
        else
        {
            return await query.AnyAsync(cg => cg.CategoryGroupEnglish == name);
        }
    }

    // Bulk operations
    public async Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryGroupIds, bool activeStatus)
    {
        var categoryGroups = await _dbSet
            .Where(cg => categoryGroupIds.Contains(cg.CategoryGroupId))
            .ToListAsync();

        foreach (var categoryGroup in categoryGroups)
        {
            categoryGroup.ActiveFlag = activeStatus;
        }

        return await _context.SaveChangesAsync();
    }

    public async Task<int> BulkUpdateKpirFlagAsync(IEnumerable<int> categoryGroupIds, bool? kpirFlag)
    {
        var categoryGroups = await _dbSet
            .Where(cg => categoryGroupIds.Contains(cg.CategoryGroupId))
            .ToListAsync();

        foreach (var categoryGroup in categoryGroups)
        {
            categoryGroup.KpirFlag = kpirFlag;
        }

        return await _context.SaveChangesAsync();
    }

    // Statistics
    public async Task<int> GetActiveCategoryGroupCountAsync()
    {
        return await _dbSet.CountAsync(cg => cg.ActiveFlag);
    }

    public async Task<int> GetKpirCategoryGroupCountAsync()
    {
        return await _dbSet.CountAsync(cg => cg.KpirFlag == true);
    }

    public async Task<int> GetCategoryGroupsWithLegacyIdCountAsync()
    {
        return await _dbSet.CountAsync(cg => cg.LegacySystemId.HasValue);
    }

    // Override DeleteAsync to use correct key
    public new async Task DeleteAsync(int id)
    {
        var entity = await GetByIdAsync(id);
        if (entity != null)
        {
            _dbSet.Remove(entity);
        }
    }
}

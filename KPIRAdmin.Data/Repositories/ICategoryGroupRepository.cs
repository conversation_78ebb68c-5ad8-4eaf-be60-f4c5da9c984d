using KPIRAdmin.Data.Entities;

namespace KPIRAdmin.Data.Repositories;

/// <summary>
/// Repository interface for CategoryGroup entity with specific business operations
/// </summary>
public interface ICategoryGroupRepository : IRepository<CategoryGroup, int>
{
    // CategoryGroup-specific operations
    Task<IEnumerable<CategoryGroup>> GetActiveCategoryGroupsAsync();
    Task<IEnumerable<CategoryGroup>> GetCategoryGroupsByKpirFlagAsync(bool kpirFlag);
    Task<IEnumerable<CategoryGroup>> SearchCategoryGroupsAsync(string searchTerm, string language = "en");
    Task<IEnumerable<CategoryGroup>> GetCategoryGroupsSortedAsync(string language = "en", bool ascending = true);
    
    // Legacy system operations
    Task<CategoryGroup?> GetCategoryGroupByLegacyIdAsync(int legacySystemId);
    Task<IEnumerable<CategoryGroup>> GetCategoryGroupsByLegacyIdsAsync(IEnumerable<int> legacySystemIds);
    Task<bool> LegacyIdExistsAsync(int legacySystemId, int? excludeId = null);
    
    // Bilingual operations
    Task<CategoryGroup?> GetCategoryGroupByNameAsync(string name, string language = "en");
    Task<bool> CategoryGroupNameExistsAsync(string name, string language = "en", int? excludeId = null);
    
    // Bulk operations
    Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryGroupIds, bool activeStatus);
    Task<int> BulkUpdateKpirFlagAsync(IEnumerable<int> categoryGroupIds, bool? kpirFlag);
    
    // Statistics
    Task<int> GetActiveCategoryGroupCountAsync();
    Task<int> GetKpirCategoryGroupCountAsync();
    Task<int> GetCategoryGroupsWithLegacyIdCountAsync();
}

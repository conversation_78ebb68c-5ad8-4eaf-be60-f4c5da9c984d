using KPIRAdmin.Data.Entities;

namespace KPIRAdmin.Data.Repositories;

/// <summary>
/// Repository interface for Category entity with specific business operations
/// </summary>
public interface ICategoryRepository : IRepository<Category, int>
{
    // Category-specific operations
    Task<IEnumerable<Category>> GetActiveCategoriesAsync();
    Task<IEnumerable<Category>> GetCategoriesByKpirFlagAsync(bool kpirFlag);
    Task<IEnumerable<Category>> SearchCategoriesAsync(string searchTerm, string language = "en");
    Task<IEnumerable<Category>> GetCategoriesSortedAsync(string language = "en", bool ascending = true);
    
    // Bilingual operations
    Task<Category?> GetCategoryByNameAsync(string name, string language = "en");
    Task<bool> CategoryNameExistsAsync(string name, string language = "en", int? excludeId = null);
    
    // Bulk operations
    Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryIds, bool activeStatus);
    Task<int> BulkUpdateKpirFlagAsync(IEnumerable<int> categoryIds, bool? kpirFlag);
    
    // Statistics
    Task<int> GetActiveCategoryCountAsync();
    Task<int> GetKpirCategoryCountAsync();
}

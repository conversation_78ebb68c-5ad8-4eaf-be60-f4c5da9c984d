using System.Linq.Expressions;

namespace KPIRAdmin.Data.Repositories;

/// <summary>
/// Generic repository interface for common CRUD operations
/// </summary>
/// <typeparam name="T">Entity type</typeparam>
public interface IRepository<T> where T : class
{
    // Read operations
    Task<T?> GetByIdAsync(int id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> GetAsync(Expression<Func<T, bool>> predicate);
    Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);

    // Paging and sorting
    Task<IEnumerable<T>> GetPagedAsync<TKey>(
        int pageNumber, 
        int pageSize, 
        Expression<Func<T, TKey>> orderBy, 
        bool ascending = true,
        Expression<Func<T, bool>>? filter = null);

    // Write operations
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task UpdateAsync(T entity);
    Task UpdateRangeAsync(IEnumerable<T> entities);
    Task DeleteAsync(T entity);
    Task DeleteAsync(int id);
    Task DeleteRangeAsync(IEnumerable<T> entities);

    // Unit of Work
    Task<int> SaveChangesAsync();
}

/// <summary>
/// Generic repository interface with additional methods for entities with specific ID types
/// </summary>
/// <typeparam name="T">Entity type</typeparam>
/// <typeparam name="TKey">Primary key type</typeparam>
public interface IRepository<T, TKey> where T : class
{
    // Read operations
    Task<T?> GetByIdAsync(TKey id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> GetAsync(Expression<Func<T, bool>> predicate);
    Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);

    // Paging and sorting
    Task<IEnumerable<T>> GetPagedAsync<TOrderKey>(
        int pageNumber, 
        int pageSize, 
        Expression<Func<T, TOrderKey>> orderBy, 
        bool ascending = true,
        Expression<Func<T, bool>>? filter = null);

    // Write operations
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task UpdateAsync(T entity);
    Task UpdateRangeAsync(IEnumerable<T> entities);
    Task DeleteAsync(T entity);
    Task DeleteAsync(TKey id);
    Task DeleteRangeAsync(IEnumerable<T> entities);

    // Unit of Work
    Task<int> SaveChangesAsync();
}

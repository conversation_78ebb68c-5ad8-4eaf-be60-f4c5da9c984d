using Microsoft.EntityFrameworkCore;
using KPIRAdmin.Data.Entities;
using System.Linq.Expressions;

namespace KPIRAdmin.Data.Repositories;

/// <summary>
/// Repository implementation for Category entity
/// </summary>
public class CategoryRepository : Repository<Category>, ICategoryRepository
{
    public CategoryRepository(KpirDbContext context) : base(context)
    {
    }

    // Override GetByIdAsync to use the correct key type
    public new async Task<Category?> GetByIdAsync(int id)
    {
        return await _dbSet.FirstOrDefaultAsync(c => c.CategoryId == id);
    }

    // Category-specific operations
    public async Task<IEnumerable<Category>> GetActiveCategoriesAsync()
    {
        return await _dbSet
            .Where(c => c.ActiveFlag)
            .OrderBy(c => c.SortOrderEnglish ?? int.MaxValue)
            .ThenBy(c => c.CategoryEnglish)
            .ToListAsync();
    }

    public async Task<IEnumerable<Category>> GetCategoriesByKpirFlagAsync(bool kpirFlag)
    {
        return await _dbSet
            .Where(c => c.KpirFlag == kpirFlag)
            .OrderBy(c => c.SortOrderEnglish ?? int.MaxValue)
            .ThenBy(c => c.CategoryEnglish)
            .ToListAsync();
    }

    public async Task<IEnumerable<Category>> SearchCategoriesAsync(string searchTerm, string language = "en")
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return await GetActiveCategoriesAsync();
        }

        var query = _dbSet.AsQueryable();

        if (language.ToLower() == "fr")
        {
            query = query.Where(c => 
                (c.CategoryFrench != null && c.CategoryFrench.Contains(searchTerm)) ||
                (c.DescriptionFrench != null && c.DescriptionFrench.Contains(searchTerm)) ||
                (c.ShortDescriptionFrench != null && c.ShortDescriptionFrench.Contains(searchTerm)));
        }
        else
        {
            query = query.Where(c => 
                (c.CategoryEnglish != null && c.CategoryEnglish.Contains(searchTerm)) ||
                (c.DescriptionEnglish != null && c.DescriptionEnglish.Contains(searchTerm)) ||
                (c.ShortDescriptionEnglish != null && c.ShortDescriptionEnglish.Contains(searchTerm)));
        }

        return await query
            .OrderBy(c => language.ToLower() == "fr" ? c.SortOrderFrench ?? int.MaxValue : c.SortOrderEnglish ?? int.MaxValue)
            .ThenBy(c => language.ToLower() == "fr" ? c.CategoryFrench : c.CategoryEnglish)
            .ToListAsync();
    }

    public async Task<IEnumerable<Category>> GetCategoriesSortedAsync(string language = "en", bool ascending = true)
    {
        var query = _dbSet.AsQueryable();

        if (language.ToLower() == "fr")
        {
            query = ascending 
                ? query.OrderBy(c => c.SortOrderFrench ?? int.MaxValue).ThenBy(c => c.CategoryFrench)
                : query.OrderByDescending(c => c.SortOrderFrench ?? int.MinValue).ThenByDescending(c => c.CategoryFrench);
        }
        else
        {
            query = ascending 
                ? query.OrderBy(c => c.SortOrderEnglish ?? int.MaxValue).ThenBy(c => c.CategoryEnglish)
                : query.OrderByDescending(c => c.SortOrderEnglish ?? int.MinValue).ThenByDescending(c => c.CategoryEnglish);
        }

        return await query.ToListAsync();
    }

    // Bilingual operations
    public async Task<Category?> GetCategoryByNameAsync(string name, string language = "en")
    {
        if (language.ToLower() == "fr")
        {
            return await _dbSet.FirstOrDefaultAsync(c => c.CategoryFrench == name);
        }
        else
        {
            return await _dbSet.FirstOrDefaultAsync(c => c.CategoryEnglish == name);
        }
    }

    public async Task<bool> CategoryNameExistsAsync(string name, string language = "en", int? excludeId = null)
    {
        var query = _dbSet.AsQueryable();

        if (excludeId.HasValue)
        {
            query = query.Where(c => c.CategoryId != excludeId.Value);
        }

        if (language.ToLower() == "fr")
        {
            return await query.AnyAsync(c => c.CategoryFrench == name);
        }
        else
        {
            return await query.AnyAsync(c => c.CategoryEnglish == name);
        }
    }

    // Bulk operations
    public async Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryIds, bool activeStatus)
    {
        var categories = await _dbSet
            .Where(c => categoryIds.Contains(c.CategoryId))
            .ToListAsync();

        foreach (var category in categories)
        {
            category.ActiveFlag = activeStatus;
        }

        return await _context.SaveChangesAsync();
    }

    public async Task<int> BulkUpdateKpirFlagAsync(IEnumerable<int> categoryIds, bool? kpirFlag)
    {
        var categories = await _dbSet
            .Where(c => categoryIds.Contains(c.CategoryId))
            .ToListAsync();

        foreach (var category in categories)
        {
            category.KpirFlag = kpirFlag;
        }

        return await _context.SaveChangesAsync();
    }

    // Statistics
    public async Task<int> GetActiveCategoryCountAsync()
    {
        return await _dbSet.CountAsync(c => c.ActiveFlag);
    }

    public async Task<int> GetKpirCategoryCountAsync()
    {
        return await _dbSet.CountAsync(c => c.KpirFlag == true);
    }

    // Override DeleteAsync to use correct key
    public new async Task DeleteAsync(int id)
    {
        var entity = await GetByIdAsync(id);
        if (entity != null)
        {
            _dbSet.Remove(entity);
        }
    }
}

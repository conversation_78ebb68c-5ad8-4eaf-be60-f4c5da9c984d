using System.ComponentModel.DataAnnotations;

namespace KPIRAdmin.Data.DTOs;

/// <summary>
/// Data Transfer Object for Category entity
/// </summary>
public class CategoryDto
{
    public int CategoryId { get; set; }

    [Display(Name = "Category (English)")]
    [StringLength(50, ErrorMessage = "Category name cannot exceed 50 characters")]
    public string? CategoryEnglish { get; set; }

    [Display(Name = "Category (French)")]
    [StringLength(50, ErrorMessage = "Category name cannot exceed 50 characters")]
    public string? CategoryFrench { get; set; }

    [Display(Name = "Description (English)")]
    [StringLength(5000, ErrorMessage = "Description cannot exceed 5000 characters")]
    public string? DescriptionEnglish { get; set; }

    [Display(Name = "Description (French)")]
    [StringLength(5000, ErrorMessage = "Description cannot exceed 5000 characters")]
    public string? DescriptionFrench { get; set; }

    [Display(Name = "Short Description (English)")]
    [StringLength(500, ErrorMessage = "Short description cannot exceed 500 characters")]
    public string? ShortDescriptionEnglish { get; set; }

    [Display(Name = "Short Description (French)")]
    [StringLength(500, ErrorMessage = "Short description cannot exceed 500 characters")]
    public string? ShortDescriptionFrench { get; set; }

    [Display(Name = "Sort Order (English)")]
    [Range(0, int.MaxValue, ErrorMessage = "Sort order must be a positive number")]
    public int? SortOrderEnglish { get; set; }

    [Display(Name = "Sort Order (French)")]
    [Range(0, int.MaxValue, ErrorMessage = "Sort order must be a positive number")]
    public int? SortOrderFrench { get; set; }

    [Display(Name = "KPIR Flag")]
    public bool? KpirFlag { get; set; }

    [Display(Name = "Active")]
    [Required(ErrorMessage = "Active flag is required")]
    public bool ActiveFlag { get; set; } = true;
}

/// <summary>
/// Simplified DTO for Category listings
/// </summary>
public class CategoryListDto
{
    public int CategoryId { get; set; }
    public string? CategoryEnglish { get; set; }
    public string? CategoryFrench { get; set; }
    public string? ShortDescriptionEnglish { get; set; }
    public string? ShortDescriptionFrench { get; set; }
    public bool ActiveFlag { get; set; }
    public bool? KpirFlag { get; set; }
}

/// <summary>
/// DTO for Category creation/editing forms
/// </summary>
public class CategoryFormDto
{
    public int? CategoryId { get; set; }

    [Display(Name = "Category (English)")]
    [StringLength(50)]
    public string? CategoryEnglish { get; set; }

    [Display(Name = "Category (French)")]
    [StringLength(50)]
    public string? CategoryFrench { get; set; }

    [Display(Name = "Description (English)")]
    [StringLength(5000)]
    [DataType(DataType.MultilineText)]
    public string? DescriptionEnglish { get; set; }

    [Display(Name = "Description (French)")]
    [StringLength(5000)]
    [DataType(DataType.MultilineText)]
    public string? DescriptionFrench { get; set; }

    [Display(Name = "Short Description (English)")]
    [StringLength(500)]
    public string? ShortDescriptionEnglish { get; set; }

    [Display(Name = "Short Description (French)")]
    [StringLength(500)]
    public string? ShortDescriptionFrench { get; set; }

    [Display(Name = "Sort Order (English)")]
    [Range(0, int.MaxValue)]
    public int? SortOrderEnglish { get; set; }

    [Display(Name = "Sort Order (French)")]
    [Range(0, int.MaxValue)]
    public int? SortOrderFrench { get; set; }

    [Display(Name = "KPIR Flag")]
    public bool? KpirFlag { get; set; }

    [Display(Name = "Active")]
    public bool ActiveFlag { get; set; } = true;
}

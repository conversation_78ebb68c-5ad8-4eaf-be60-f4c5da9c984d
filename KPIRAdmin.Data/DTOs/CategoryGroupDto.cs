using System.ComponentModel.DataAnnotations;

namespace KPIRAdmin.Data.DTOs;

/// <summary>
/// Data Transfer Object for CategoryGroup entity
/// </summary>
public class CategoryGroupDto
{
    public int CategoryGroupId { get; set; }

    [Display(Name = "Legacy System ID")]
    public int? LegacySystemId { get; set; }

    [Display(Name = "Category Group (English)")]
    [StringLength(50, ErrorMessage = "Category group name cannot exceed 50 characters")]
    public string? CategoryGroupEnglish { get; set; }

    [Display(Name = "Category Group (French)")]
    [StringLength(50, ErrorMessage = "Category group name cannot exceed 50 characters")]
    public string? CategoryGroupFrench { get; set; }

    [Display(Name = "Description (English)")]
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? DescriptionEnglish { get; set; }

    [Display(Name = "Description (French)")]
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? DescriptionFrench { get; set; }

    [Display(Name = "Short Description (English)")]
    [StringLength(500, ErrorMessage = "Short description cannot exceed 500 characters")]
    public string? ShortDescriptionEnglish { get; set; }

    [Display(Name = "Short Description (French)")]
    [StringLength(500, ErrorMessage = "Short description cannot exceed 500 characters")]
    public string? ShortDescriptionFrench { get; set; }

    [Display(Name = "Sort Order (English)")]
    [Range(0, int.MaxValue, ErrorMessage = "Sort order must be a positive number")]
    public int? SortOrderEnglish { get; set; }

    [Display(Name = "Sort Order (French)")]
    [Range(0, int.MaxValue, ErrorMessage = "Sort order must be a positive number")]
    public int? SortOrderFrench { get; set; }

    [Display(Name = "KPIR Flag")]
    public bool? KpirFlag { get; set; }

    [Display(Name = "Active")]
    [Required(ErrorMessage = "Active flag is required")]
    public bool ActiveFlag { get; set; } = true;
}

/// <summary>
/// Simplified DTO for CategoryGroup listings
/// </summary>
public class CategoryGroupListDto
{
    public int CategoryGroupId { get; set; }
    public int? LegacySystemId { get; set; }
    public string? CategoryGroupEnglish { get; set; }
    public string? CategoryGroupFrench { get; set; }
    public string? ShortDescriptionEnglish { get; set; }
    public string? ShortDescriptionFrench { get; set; }
    public bool ActiveFlag { get; set; }
    public bool? KpirFlag { get; set; }
}

/// <summary>
/// DTO for CategoryGroup creation/editing forms
/// </summary>
public class CategoryGroupFormDto
{
    public int? CategoryGroupId { get; set; }

    [Display(Name = "Legacy System ID")]
    public int? LegacySystemId { get; set; }

    [Display(Name = "Category Group (English)")]
    [StringLength(50)]
    public string? CategoryGroupEnglish { get; set; }

    [Display(Name = "Category Group (French)")]
    [StringLength(50)]
    public string? CategoryGroupFrench { get; set; }

    [Display(Name = "Description (English)")]
    [StringLength(1000)]
    [DataType(DataType.MultilineText)]
    public string? DescriptionEnglish { get; set; }

    [Display(Name = "Description (French)")]
    [StringLength(1000)]
    [DataType(DataType.MultilineText)]
    public string? DescriptionFrench { get; set; }

    [Display(Name = "Short Description (English)")]
    [StringLength(500)]
    public string? ShortDescriptionEnglish { get; set; }

    [Display(Name = "Short Description (French)")]
    [StringLength(500)]
    public string? ShortDescriptionFrench { get; set; }

    [Display(Name = "Sort Order (English)")]
    [Range(0, int.MaxValue)]
    public int? SortOrderEnglish { get; set; }

    [Display(Name = "Sort Order (French)")]
    [Range(0, int.MaxValue)]
    public int? SortOrderFrench { get; set; }

    [Display(Name = "KPIR Flag")]
    public bool? KpirFlag { get; set; }

    [Display(Name = "Active")]
    public bool ActiveFlag { get; set; } = true;
}

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KPIRAdmin.Data.Entities;

/// <summary>
/// Represents a category group entity with bilingual support
/// </summary>
[Table("category_groups")]
public class CategoryGroup : IBaseEntity
{
    [Key]
    [Column("category_group_id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int CategoryGroupId { get; set; }

    [Column("legacy_system_id")]
    public int? LegacySystemId { get; set; }

    [Column("category_group_e")]
    [MaxLength(50)]
    public string? CategoryGroupEnglish { get; set; }

    [Column("category_group_f")]
    [MaxLength(50)]
    public string? CategoryGroupFrench { get; set; }

    [Column("desc_e")]
    [MaxLength(1000)]
    public string? DescriptionEnglish { get; set; }

    [Column("desc_f")]
    [MaxLength(1000)]
    public string? DescriptionFrench { get; set; }

    [Column("short_desc_e")]
    [MaxLength(500)]
    public string? ShortDescriptionEnglish { get; set; }

    [Column("short_desc_f")]
    [MaxLength(500)]
    public string? ShortDescriptionFrench { get; set; }

    [Column("sort_order_e")]
    public int? SortOrderEnglish { get; set; }

    [Column("sort_order_f")]
    public int? SortOrderFrench { get; set; }

    [Column("kpir_flag")]
    public bool? KpirFlag { get; set; }

    [Column("active_flag")]
    [Required]
    public bool ActiveFlag { get; set; } = true;

    // Navigation properties for potential hierarchical relationships
    // These can be added later when the relationships are clarified
}

﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KPIRAdmin.Data.Entities;

/// <summary>
/// Represents a category entity with bilingual support
/// </summary>
[Table("categories")]
public class Category : IBaseEntity
{
    [Key]
    [Column("category_id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int CategoryId { get; set; }

    [Column("category_e")]
    [MaxLength(50)]
    public string? CategoryEnglish { get; set; }

    [Column("category_f")]
    [MaxLength(50)]
    public string? CategoryFrench { get; set; }

    [Column("desc_e")]
    [MaxLength(5000)]
    public string? DescriptionEnglish { get; set; }

    [Column("desc_f")]
    [MaxLength(5000)]
    public string? DescriptionFrench { get; set; }

    [Column("short_desc_e")]
    [MaxLength(500)]
    public string? ShortDescriptionEnglish { get; set; }

    [Column("short_desc_f")]
    [MaxLength(500)]
    public string? ShortDescriptionFrench { get; set; }

    [Column("sort_order_e")]
    public int? SortOrderEnglish { get; set; }

    [Column("sort_order_f")]
    public int? SortOrderFrench { get; set; }

    [Column("kpir_flag")]
    public bool? KpirFlag { get; set; }

    [Column("active_flag")]
    [Required]
    public bool ActiveFlag { get; set; } = true;

    // Navigation properties for potential hierarchical relationships
    // These can be added later when the relationships are clarified
}

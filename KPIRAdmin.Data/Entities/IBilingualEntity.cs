namespace KPIRAdmin.Data.Entities;

/// <summary>
/// Interface for entities that support bilingual content (English and French)
/// </summary>
public interface IBilingualEntity
{
    string? DescriptionEnglish { get; set; }
    string? DescriptionFrench { get; set; }
    string? ShortDescriptionEnglish { get; set; }
    string? ShortDescriptionFrench { get; set; }
    int? SortOrderEnglish { get; set; }
    int? SortOrderFrench { get; set; }
}

/// <summary>
/// Interface for entities that have active/inactive status
/// </summary>
public interface IActivatable
{
    bool ActiveFlag { get; set; }
}

/// <summary>
/// Interface for entities that support KPIR flag
/// </summary>
public interface IKpirFlaggable
{
    bool? KpirFlag { get; set; }
}

/// <summary>
/// Base interface combining common entity features
/// </summary>
public interface IBaseEntity : IBilingualEntity, IActivatable, IKpirFlaggable
{
}

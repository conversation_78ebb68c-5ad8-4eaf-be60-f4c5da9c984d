{"format": 1, "restore": {"/home/<USER>/repos/KPIRAdmin/KPIRAdmin.Data/KPIRAdmin.Data.csproj": {}}, "projects": {"/home/<USER>/repos/KPIRAdmin/KPIRAdmin.Data/KPIRAdmin.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/repos/KPIRAdmin/KPIRAdmin.Data/KPIRAdmin.Data.csproj", "projectName": "KPIRAdmin.Data", "projectPath": "/home/<USER>/repos/KPIRAdmin/KPIRAdmin.Data/KPIRAdmin.Data.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/repos/KPIRAdmin/KPIRAdmin.Data/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/snap/dotnet-sdk/256/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}
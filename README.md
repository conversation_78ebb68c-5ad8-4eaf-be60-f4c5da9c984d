# KPIR Admin - Hierarchical Data Management System

A .NET Core web application for managing hierarchical (multi-level parent/child) data in SQL Server with bilingual support (English/French).

## Project Structure

```
KPIRAdmin/
├── KPIRAdmin.sln                 # Visual Studio Solution File
├── KPIRAdmin.Web/                # Main Web Application (ASP.NET Core MVC)
├── KPIRAdmin.Services/           # Business Logic Layer
├── KPIRAdmin.Data/               # Data Access Layer (Entity Framework Core)
├── specs.md                      # Project Specifications
├── kpir_tables.sql              # Database DDL
└── README.md                     # This file
```

## Technology Stack

- **Backend**: .NET 8.0 (ASP.NET Core MVC)
- **Frontend**: Razor Pages with Bootstrap
- **Database**: SQL Server 2016
- **ORM**: Entity Framework Core 8.0
- **Hosting**: IIS (Internet Information Services)
- **Development**: Visual Studio

## Project Dependencies

### KPIRAdmin.Data
- Microsoft.EntityFrameworkCore.SqlServer 8.0.0
- Microsoft.EntityFrameworkCore.Tools 8.0.0

### KPIRAdmin.Services
- References: KPIRAdmin.Data

### KPIRAdmin.Web
- Microsoft.EntityFrameworkCore.SqlServer 8.0.0
- References: KPIRAdmin.Services

## Database Schema

Current tables:
- **categories**: Main category entities with bilingual support
- **category_groups**: Category group entities with bilingual support

Both tables include:
- Bilingual fields (English `_e` and French `_f` suffixes)
- Description fields (short and long)
- Sort order fields
- KPIR and active flags

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- SQL Server 2016 or later
- Visual Studio 2022 (recommended)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd KPIRAdmin
   ```

2. **Open in Visual Studio**
   - Open `KPIRAdmin.sln` in Visual Studio
   - Restore NuGet packages (should happen automatically)

3. **Configure Database Connection**
   - Update connection string in `KPIRAdmin.Web/appsettings.json`
   - Run database migrations (to be created)

4. **Build and Run**
   ```bash
   dotnet build
   dotnet run --project KPIRAdmin.Web
   ```

## Features (Planned)

- **CRUD Operations**: Create, Read, Update, Delete for all entities
- **Hierarchical Navigation**: Tree views and master-detail pages
- **Bilingual Support**: English and French content management
- **Search & Filter**: Advanced filtering capabilities
- **Pagination & Sorting**: Efficient data browsing
- **Authentication**: ASP.NET Core Identity
- **Authorization**: Role-based access control
- **Responsive Design**: Bootstrap-based UI

## Development Status

- [x] Project structure created
- [x] NuGet packages installed
- [x] Project references configured
- [ ] Entity models
- [ ] DbContext configuration
- [ ] Repository pattern
- [ ] Service layer
- [ ] Controllers
- [ ] Views
- [ ] Authentication
- [ ] Testing

## Contributing

This project follows standard .NET development practices:
- Use Entity Framework Code-First approach
- Follow Repository and Service patterns
- Implement proper error handling
- Write unit tests for business logic
- Use dependency injection

## License

[License information to be added]

using KPIRAdmin.Data.DTOs;
using KPIRAdmin.Data.Entities;

namespace KPIRAdmin.Services.Interfaces;

/// <summary>
/// Service interface for Category business logic
/// </summary>
public interface ICategoryService
{
    // CRUD operations
    Task<CategoryDto?> GetCategoryByIdAsync(int id);
    Task<IEnumerable<CategoryListDto>> GetAllCategoriesAsync();
    Task<IEnumerable<CategoryListDto>> GetActiveCategoriesAsync();
    Task<CategoryDto> CreateCategoryAsync(CategoryFormDto categoryForm);
    Task<CategoryDto> UpdateCategoryAsync(int id, CategoryFormDto categoryForm);
    Task<bool> DeleteCategoryAsync(int id);
    Task<bool> SoftDeleteCategoryAsync(int id);

    // Search and filtering
    Task<IEnumerable<CategoryListDto>> SearchCategoriesAsync(string searchTerm, string language = "en");
    Task<IEnumerable<CategoryListDto>> GetCategoriesByKpirFlagAsync(bool kpirFlag);
    Task<IEnumerable<CategoryListDto>> GetCategoriesSortedAsync(string language = "en", bool ascending = true);

    // Paging
    Task<(IEnumerable<CategoryListDto> Categories, int TotalCount)> GetCategoriesPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null, 
        string language = "en",
        bool? activeOnly = null,
        bool? kpirFlag = null);

    // Validation
    Task<bool> CategoryNameExistsAsync(string name, string language = "en", int? excludeId = null);
    Task<bool> CanDeleteCategoryAsync(int id);

    // Bulk operations
    Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryIds, bool activeStatus);
    Task<int> BulkUpdateKpirFlagAsync(IEnumerable<int> categoryIds, bool? kpirFlag);
    Task<int> BulkDeleteCategoriesAsync(IEnumerable<int> categoryIds);

    // Statistics and reporting
    Task<int> GetActiveCategoryCountAsync();
    Task<int> GetKpirCategoryCountAsync();
    Task<int> GetTotalCategoryCountAsync();

    // Import/Export helpers
    Task<IEnumerable<CategoryDto>> ImportCategoriesAsync(IEnumerable<CategoryFormDto> categories);
    Task<IEnumerable<CategoryDto>> ExportCategoriesAsync(bool activeOnly = true);

    // Utility methods
    Task<bool> CategoryExistsAsync(int id);
    CategoryDto MapToDto(Category category);
    Category MapToEntity(CategoryFormDto categoryForm);
}

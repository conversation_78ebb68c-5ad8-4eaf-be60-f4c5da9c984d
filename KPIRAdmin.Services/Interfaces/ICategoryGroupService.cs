using KPIRAdmin.Data.DTOs;
using KPIRAdmin.Data.Entities;

namespace KPIRAdmin.Services.Interfaces;

/// <summary>
/// Service interface for CategoryGroup business logic
/// </summary>
public interface ICategoryGroupService
{
    // CRUD operations
    Task<CategoryGroupDto?> GetCategoryGroupByIdAsync(int id);
    Task<IEnumerable<CategoryGroupListDto>> GetAllCategoryGroupsAsync();
    Task<IEnumerable<CategoryGroupListDto>> GetActiveCategoryGroupsAsync();
    Task<CategoryGroupDto> CreateCategoryGroupAsync(CategoryGroupFormDto categoryGroupForm);
    Task<CategoryGroupDto> UpdateCategoryGroupAsync(int id, CategoryGroupFormDto categoryGroupForm);
    Task<bool> DeleteCategoryGroupAsync(int id);
    Task<bool> SoftDeleteCategoryGroupAsync(int id);

    // Search and filtering
    Task<IEnumerable<CategoryGroupListDto>> SearchCategoryGroupsAsync(string searchTerm, string language = "en");
    Task<IEnumerable<CategoryGroupListDto>> GetCategoryGroupsByKpirFlagAsync(bool kpirFlag);
    Task<IEnumerable<CategoryGroupListDto>> GetCategoryGroupsSortedAsync(string language = "en", bool ascending = true);

    // Legacy system operations
    Task<CategoryGroupDto?> GetCategoryGroupByLegacyIdAsync(int legacySystemId);
    Task<IEnumerable<CategoryGroupListDto>> GetCategoryGroupsByLegacyIdsAsync(IEnumerable<int> legacySystemIds);
    Task<bool> LegacyIdExistsAsync(int legacySystemId, int? excludeId = null);

    // Paging
    Task<(IEnumerable<CategoryGroupListDto> CategoryGroups, int TotalCount)> GetCategoryGroupsPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null, 
        string language = "en",
        bool? activeOnly = null,
        bool? kpirFlag = null,
        bool? hasLegacyId = null);

    // Validation
    Task<bool> CategoryGroupNameExistsAsync(string name, string language = "en", int? excludeId = null);
    Task<bool> CanDeleteCategoryGroupAsync(int id);

    // Bulk operations
    Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryGroupIds, bool activeStatus);
    Task<int> BulkUpdateKpirFlagAsync(IEnumerable<int> categoryGroupIds, bool? kpirFlag);
    Task<int> BulkDeleteCategoryGroupsAsync(IEnumerable<int> categoryGroupIds);

    // Statistics and reporting
    Task<int> GetActiveCategoryGroupCountAsync();
    Task<int> GetKpirCategoryGroupCountAsync();
    Task<int> GetCategoryGroupsWithLegacyIdCountAsync();
    Task<int> GetTotalCategoryGroupCountAsync();

    // Import/Export helpers
    Task<IEnumerable<CategoryGroupDto>> ImportCategoryGroupsAsync(IEnumerable<CategoryGroupFormDto> categoryGroups);
    Task<IEnumerable<CategoryGroupDto>> ExportCategoryGroupsAsync(bool activeOnly = true);

    // Utility methods
    Task<bool> CategoryGroupExistsAsync(int id);
    CategoryGroupDto MapToDto(CategoryGroup categoryGroup);
    CategoryGroup MapToEntity(CategoryGroupFormDto categoryGroupForm);
}

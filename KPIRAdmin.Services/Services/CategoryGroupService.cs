using KPIRAdmin.Data.DTOs;
using KPIRAdmin.Data.Entities;
using KPIRAdmin.Data.Repositories;
using KPIRAdmin.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace KPIRAdmin.Services.Services;

/// <summary>
/// Service implementation for CategoryGroup business logic
/// </summary>
public class CategoryGroupService : ICategoryGroupService
{
    private readonly ICategoryGroupRepository _categoryGroupRepository;
    private readonly ILogger<CategoryGroupService> _logger;

    public CategoryGroupService(ICategoryGroupRepository categoryGroupRepository, ILogger<CategoryGroupService> logger)
    {
        _categoryGroupRepository = categoryGroupRepository ?? throw new ArgumentNullException(nameof(categoryGroupRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    // CRUD operations
    public async Task<CategoryGroupDto?> GetCategoryGroupByIdAsync(int id)
    {
        try
        {
            var categoryGroup = await _categoryGroupRepository.GetByIdAsync(id);
            return categoryGroup != null ? MapToDto(categoryGroup) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category group by ID {CategoryGroupId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<CategoryGroupListDto>> GetAllCategoryGroupsAsync()
    {
        try
        {
            var categoryGroups = await _categoryGroupRepository.GetAllAsync();
            return categoryGroups.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all category groups");
            throw;
        }
    }

    public async Task<IEnumerable<CategoryGroupListDto>> GetActiveCategoryGroupsAsync()
    {
        try
        {
            var categoryGroups = await _categoryGroupRepository.GetActiveCategoryGroupsAsync();
            return categoryGroups.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active category groups");
            throw;
        }
    }

    public async Task<CategoryGroupDto> CreateCategoryGroupAsync(CategoryGroupFormDto categoryGroupForm)
    {
        try
        {
            // Validate business rules
            await ValidateCreateCategoryGroupAsync(categoryGroupForm);

            var categoryGroup = MapToEntity(categoryGroupForm);
            var createdCategoryGroup = await _categoryGroupRepository.AddAsync(categoryGroup);
            await _categoryGroupRepository.SaveChangesAsync();

            _logger.LogInformation("Created category group with ID {CategoryGroupId}", createdCategoryGroup.CategoryGroupId);
            return MapToDto(createdCategoryGroup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating category group");
            throw;
        }
    }

    public async Task<CategoryGroupDto> UpdateCategoryGroupAsync(int id, CategoryGroupFormDto categoryGroupForm)
    {
        try
        {
            var existingCategoryGroup = await _categoryGroupRepository.GetByIdAsync(id);
            if (existingCategoryGroup == null)
            {
                throw new ArgumentException($"Category group with ID {id} not found");
            }

            // Validate business rules
            await ValidateUpdateCategoryGroupAsync(id, categoryGroupForm);

            // Update properties
            UpdateCategoryGroupProperties(existingCategoryGroup, categoryGroupForm);
            
            await _categoryGroupRepository.UpdateAsync(existingCategoryGroup);
            await _categoryGroupRepository.SaveChangesAsync();

            _logger.LogInformation("Updated category group with ID {CategoryGroupId}", id);
            return MapToDto(existingCategoryGroup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating category group with ID {CategoryGroupId}", id);
            throw;
        }
    }

    public async Task<bool> DeleteCategoryGroupAsync(int id)
    {
        try
        {
            if (!await CanDeleteCategoryGroupAsync(id))
            {
                return false;
            }

            await _categoryGroupRepository.DeleteAsync(id);
            var result = await _categoryGroupRepository.SaveChangesAsync();

            _logger.LogInformation("Deleted category group with ID {CategoryGroupId}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category group with ID {CategoryGroupId}", id);
            throw;
        }
    }

    public async Task<bool> SoftDeleteCategoryGroupAsync(int id)
    {
        try
        {
            var categoryGroup = await _categoryGroupRepository.GetByIdAsync(id);
            if (categoryGroup == null)
            {
                return false;
            }

            categoryGroup.ActiveFlag = false;
            await _categoryGroupRepository.UpdateAsync(categoryGroup);
            var result = await _categoryGroupRepository.SaveChangesAsync();

            _logger.LogInformation("Soft deleted category group with ID {CategoryGroupId}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error soft deleting category group with ID {CategoryGroupId}", id);
            throw;
        }
    }

    // Search and filtering
    public async Task<IEnumerable<CategoryGroupListDto>> SearchCategoryGroupsAsync(string searchTerm, string language = "en")
    {
        try
        {
            var categoryGroups = await _categoryGroupRepository.SearchCategoryGroupsAsync(searchTerm, language);
            return categoryGroups.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching category groups with term '{SearchTerm}' in language '{Language}'", searchTerm, language);
            throw;
        }
    }

    public async Task<IEnumerable<CategoryGroupListDto>> GetCategoryGroupsByKpirFlagAsync(bool kpirFlag)
    {
        try
        {
            var categoryGroups = await _categoryGroupRepository.GetCategoryGroupsByKpirFlagAsync(kpirFlag);
            return categoryGroups.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category groups by KPIR flag {KpirFlag}", kpirFlag);
            throw;
        }
    }

    public async Task<IEnumerable<CategoryGroupListDto>> GetCategoryGroupsSortedAsync(string language = "en", bool ascending = true)
    {
        try
        {
            var categoryGroups = await _categoryGroupRepository.GetCategoryGroupsSortedAsync(language, ascending);
            return categoryGroups.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sorted category groups in language '{Language}', ascending: {Ascending}", language, ascending);
            throw;
        }
    }

    // Legacy system operations
    public async Task<CategoryGroupDto?> GetCategoryGroupByLegacyIdAsync(int legacySystemId)
    {
        try
        {
            var categoryGroup = await _categoryGroupRepository.GetCategoryGroupByLegacyIdAsync(legacySystemId);
            return categoryGroup != null ? MapToDto(categoryGroup) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category group by legacy ID {LegacySystemId}", legacySystemId);
            throw;
        }
    }

    public async Task<IEnumerable<CategoryGroupListDto>> GetCategoryGroupsByLegacyIdsAsync(IEnumerable<int> legacySystemIds)
    {
        try
        {
            var categoryGroups = await _categoryGroupRepository.GetCategoryGroupsByLegacyIdsAsync(legacySystemIds);
            return categoryGroups.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category groups by legacy IDs");
            throw;
        }
    }

    public async Task<bool> LegacyIdExistsAsync(int legacySystemId, int? excludeId = null)
    {
        return await _categoryGroupRepository.LegacyIdExistsAsync(legacySystemId, excludeId);
    }

    // Validation
    public async Task<bool> CategoryGroupNameExistsAsync(string name, string language = "en", int? excludeId = null)
    {
        return await _categoryGroupRepository.CategoryGroupNameExistsAsync(name, language, excludeId);
    }

    public async Task<bool> CanDeleteCategoryGroupAsync(int id)
    {
        // Add business logic here to check if category group can be deleted
        // For example, check if it has related records
        var exists = await _categoryGroupRepository.ExistsAsync(cg => cg.CategoryGroupId == id);
        return exists;
    }

    // Statistics
    public async Task<int> GetActiveCategoryGroupCountAsync()
    {
        return await _categoryGroupRepository.GetActiveCategoryGroupCountAsync();
    }

    public async Task<int> GetKpirCategoryGroupCountAsync()
    {
        return await _categoryGroupRepository.GetKpirCategoryGroupCountAsync();
    }

    public async Task<int> GetCategoryGroupsWithLegacyIdCountAsync()
    {
        return await _categoryGroupRepository.GetCategoryGroupsWithLegacyIdCountAsync();
    }

    public async Task<int> GetTotalCategoryGroupCountAsync()
    {
        return await _categoryGroupRepository.CountAsync();
    }

    // Utility methods
    public async Task<bool> CategoryGroupExistsAsync(int id)
    {
        return await _categoryGroupRepository.ExistsAsync(cg => cg.CategoryGroupId == id);
    }

    public CategoryGroupDto MapToDto(CategoryGroup categoryGroup)
    {
        return new CategoryGroupDto
        {
            CategoryGroupId = categoryGroup.CategoryGroupId,
            LegacySystemId = categoryGroup.LegacySystemId,
            CategoryGroupEnglish = categoryGroup.CategoryGroupEnglish,
            CategoryGroupFrench = categoryGroup.CategoryGroupFrench,
            DescriptionEnglish = categoryGroup.DescriptionEnglish,
            DescriptionFrench = categoryGroup.DescriptionFrench,
            ShortDescriptionEnglish = categoryGroup.ShortDescriptionEnglish,
            ShortDescriptionFrench = categoryGroup.ShortDescriptionFrench,
            SortOrderEnglish = categoryGroup.SortOrderEnglish,
            SortOrderFrench = categoryGroup.SortOrderFrench,
            KpirFlag = categoryGroup.KpirFlag,
            ActiveFlag = categoryGroup.ActiveFlag
        };
    }

    public CategoryGroup MapToEntity(CategoryGroupFormDto categoryGroupForm)
    {
        return new CategoryGroup
        {
            LegacySystemId = categoryGroupForm.LegacySystemId,
            CategoryGroupEnglish = categoryGroupForm.CategoryGroupEnglish,
            CategoryGroupFrench = categoryGroupForm.CategoryGroupFrench,
            DescriptionEnglish = categoryGroupForm.DescriptionEnglish,
            DescriptionFrench = categoryGroupForm.DescriptionFrench,
            ShortDescriptionEnglish = categoryGroupForm.ShortDescriptionEnglish,
            ShortDescriptionFrench = categoryGroupForm.ShortDescriptionFrench,
            SortOrderEnglish = categoryGroupForm.SortOrderEnglish,
            SortOrderFrench = categoryGroupForm.SortOrderFrench,
            KpirFlag = categoryGroupForm.KpirFlag,
            ActiveFlag = categoryGroupForm.ActiveFlag
        };
    }

    private CategoryGroupListDto MapToListDto(CategoryGroup categoryGroup)
    {
        return new CategoryGroupListDto
        {
            CategoryGroupId = categoryGroup.CategoryGroupId,
            LegacySystemId = categoryGroup.LegacySystemId,
            CategoryGroupEnglish = categoryGroup.CategoryGroupEnglish,
            CategoryGroupFrench = categoryGroup.CategoryGroupFrench,
            ShortDescriptionEnglish = categoryGroup.ShortDescriptionEnglish,
            ShortDescriptionFrench = categoryGroup.ShortDescriptionFrench,
            ActiveFlag = categoryGroup.ActiveFlag,
            KpirFlag = categoryGroup.KpirFlag
        };
    }

    private async Task ValidateCreateCategoryGroupAsync(CategoryGroupFormDto categoryGroupForm)
    {
        // Add validation logic here
        if (!string.IsNullOrEmpty(categoryGroupForm.CategoryGroupEnglish))
        {
            if (await CategoryGroupNameExistsAsync(categoryGroupForm.CategoryGroupEnglish, "en"))
            {
                throw new ArgumentException("Category group name already exists in English");
            }
        }

        if (!string.IsNullOrEmpty(categoryGroupForm.CategoryGroupFrench))
        {
            if (await CategoryGroupNameExistsAsync(categoryGroupForm.CategoryGroupFrench, "fr"))
            {
                throw new ArgumentException("Category group name already exists in French");
            }
        }

        if (categoryGroupForm.LegacySystemId.HasValue)
        {
            if (await LegacyIdExistsAsync(categoryGroupForm.LegacySystemId.Value))
            {
                throw new ArgumentException("Legacy system ID already exists");
            }
        }
    }

    private async Task ValidateUpdateCategoryGroupAsync(int id, CategoryGroupFormDto categoryGroupForm)
    {
        // Add validation logic here
        if (!string.IsNullOrEmpty(categoryGroupForm.CategoryGroupEnglish))
        {
            if (await CategoryGroupNameExistsAsync(categoryGroupForm.CategoryGroupEnglish, "en", id))
            {
                throw new ArgumentException("Category group name already exists in English");
            }
        }

        if (!string.IsNullOrEmpty(categoryGroupForm.CategoryGroupFrench))
        {
            if (await CategoryGroupNameExistsAsync(categoryGroupForm.CategoryGroupFrench, "fr", id))
            {
                throw new ArgumentException("Category group name already exists in French");
            }
        }

        if (categoryGroupForm.LegacySystemId.HasValue)
        {
            if (await LegacyIdExistsAsync(categoryGroupForm.LegacySystemId.Value, id))
            {
                throw new ArgumentException("Legacy system ID already exists");
            }
        }
    }

    private void UpdateCategoryGroupProperties(CategoryGroup categoryGroup, CategoryGroupFormDto categoryGroupForm)
    {
        categoryGroup.LegacySystemId = categoryGroupForm.LegacySystemId;
        categoryGroup.CategoryGroupEnglish = categoryGroupForm.CategoryGroupEnglish;
        categoryGroup.CategoryGroupFrench = categoryGroupForm.CategoryGroupFrench;
        categoryGroup.DescriptionEnglish = categoryGroupForm.DescriptionEnglish;
        categoryGroup.DescriptionFrench = categoryGroupForm.DescriptionFrench;
        categoryGroup.ShortDescriptionEnglish = categoryGroupForm.ShortDescriptionEnglish;
        categoryGroup.ShortDescriptionFrench = categoryGroupForm.ShortDescriptionFrench;
        categoryGroup.SortOrderEnglish = categoryGroupForm.SortOrderEnglish;
        categoryGroup.SortOrderFrench = categoryGroupForm.SortOrderFrench;
        categoryGroup.KpirFlag = categoryGroupForm.KpirFlag;
        categoryGroup.ActiveFlag = categoryGroupForm.ActiveFlag;
    }

    // Placeholder implementations for interface completeness
    public Task<(IEnumerable<CategoryGroupListDto> CategoryGroups, int TotalCount)> GetCategoryGroupsPagedAsync(int pageNumber, int pageSize, string? searchTerm = null, string language = "en", bool? activeOnly = null, bool? kpirFlag = null, bool? hasLegacyId = null)
    {
        throw new NotImplementedException("Will be implemented in next iteration");
    }

    public Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryGroupIds, bool activeStatus)
    {
        return _categoryGroupRepository.BulkUpdateActiveStatusAsync(categoryGroupIds, activeStatus);
    }

    public Task<int> BulkUpdateKpirFlagAsync(IEnumerable<int> categoryGroupIds, bool? kpirFlag)
    {
        return _categoryGroupRepository.BulkUpdateKpirFlagAsync(categoryGroupIds, kpirFlag);
    }

    public Task<int> BulkDeleteCategoryGroupsAsync(IEnumerable<int> categoryGroupIds)
    {
        throw new NotImplementedException("Will be implemented in next iteration");
    }

    public Task<IEnumerable<CategoryGroupDto>> ImportCategoryGroupsAsync(IEnumerable<CategoryGroupFormDto> categoryGroups)
    {
        throw new NotImplementedException("Will be implemented in next iteration");
    }

    public Task<IEnumerable<CategoryGroupDto>> ExportCategoryGroupsAsync(bool activeOnly = true)
    {
        throw new NotImplementedException("Will be implemented in next iteration");
    }
}

using KPIRAdmin.Data.DTOs;
using KPIRAdmin.Data.Entities;
using KPIRAdmin.Data.Repositories;
using KPIRAdmin.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace KPIRAdmin.Services.Services;

/// <summary>
/// Service implementation for Category business logic
/// </summary>
public class CategoryService : ICategoryService
{
    private readonly ICategoryRepository _categoryRepository;
    private readonly ILogger<CategoryService> _logger;

    public CategoryService(ICategoryRepository categoryRepository, ILogger<CategoryService> logger)
    {
        _categoryRepository = categoryRepository ?? throw new ArgumentNullException(nameof(categoryRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    // CRUD operations
    public async Task<CategoryDto?> GetCategoryByIdAsync(int id)
    {
        try
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            return category != null ? MapToDto(category) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category by ID {CategoryId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<CategoryListDto>> GetAllCategoriesAsync()
    {
        try
        {
            var categories = await _categoryRepository.GetAllAsync();
            return categories.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all categories");
            throw;
        }
    }

    public async Task<IEnumerable<CategoryListDto>> GetActiveCategoriesAsync()
    {
        try
        {
            var categories = await _categoryRepository.GetActiveCategoriesAsync();
            return categories.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active categories");
            throw;
        }
    }

    public async Task<CategoryDto> CreateCategoryAsync(CategoryFormDto categoryForm)
    {
        try
        {
            // Validate business rules
            await ValidateCreateCategoryAsync(categoryForm);

            var category = MapToEntity(categoryForm);
            var createdCategory = await _categoryRepository.AddAsync(category);
            await _categoryRepository.SaveChangesAsync();

            _logger.LogInformation("Created category with ID {CategoryId}", createdCategory.CategoryId);
            return MapToDto(createdCategory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating category");
            throw;
        }
    }

    public async Task<CategoryDto> UpdateCategoryAsync(int id, CategoryFormDto categoryForm)
    {
        try
        {
            var existingCategory = await _categoryRepository.GetByIdAsync(id);
            if (existingCategory == null)
            {
                throw new ArgumentException($"Category with ID {id} not found");
            }

            // Validate business rules
            await ValidateUpdateCategoryAsync(id, categoryForm);

            // Update properties
            UpdateCategoryProperties(existingCategory, categoryForm);
            
            await _categoryRepository.UpdateAsync(existingCategory);
            await _categoryRepository.SaveChangesAsync();

            _logger.LogInformation("Updated category with ID {CategoryId}", id);
            return MapToDto(existingCategory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating category with ID {CategoryId}", id);
            throw;
        }
    }

    public async Task<bool> DeleteCategoryAsync(int id)
    {
        try
        {
            if (!await CanDeleteCategoryAsync(id))
            {
                return false;
            }

            await _categoryRepository.DeleteAsync(id);
            var result = await _categoryRepository.SaveChangesAsync();

            _logger.LogInformation("Deleted category with ID {CategoryId}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category with ID {CategoryId}", id);
            throw;
        }
    }

    public async Task<bool> SoftDeleteCategoryAsync(int id)
    {
        try
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null)
            {
                return false;
            }

            category.ActiveFlag = false;
            await _categoryRepository.UpdateAsync(category);
            var result = await _categoryRepository.SaveChangesAsync();

            _logger.LogInformation("Soft deleted category with ID {CategoryId}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error soft deleting category with ID {CategoryId}", id);
            throw;
        }
    }

    // Search and filtering
    public async Task<IEnumerable<CategoryListDto>> SearchCategoriesAsync(string searchTerm, string language = "en")
    {
        try
        {
            var categories = await _categoryRepository.SearchCategoriesAsync(searchTerm, language);
            return categories.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching categories with term '{SearchTerm}' in language '{Language}'", searchTerm, language);
            throw;
        }
    }

    public async Task<IEnumerable<CategoryListDto>> GetCategoriesByKpirFlagAsync(bool kpirFlag)
    {
        try
        {
            var categories = await _categoryRepository.GetCategoriesByKpirFlagAsync(kpirFlag);
            return categories.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting categories by KPIR flag {KpirFlag}", kpirFlag);
            throw;
        }
    }

    public async Task<IEnumerable<CategoryListDto>> GetCategoriesSortedAsync(string language = "en", bool ascending = true)
    {
        try
        {
            var categories = await _categoryRepository.GetCategoriesSortedAsync(language, ascending);
            return categories.Select(MapToListDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sorted categories in language '{Language}', ascending: {Ascending}", language, ascending);
            throw;
        }
    }

    // Validation
    public async Task<bool> CategoryNameExistsAsync(string name, string language = "en", int? excludeId = null)
    {
        return await _categoryRepository.CategoryNameExistsAsync(name, language, excludeId);
    }

    public async Task<bool> CanDeleteCategoryAsync(int id)
    {
        // Add business logic here to check if category can be deleted
        // For example, check if it has related records
        var exists = await _categoryRepository.ExistsAsync(c => c.CategoryId == id);
        return exists;
    }

    // Statistics
    public async Task<int> GetActiveCategoryCountAsync()
    {
        return await _categoryRepository.GetActiveCategoryCountAsync();
    }

    public async Task<int> GetKpirCategoryCountAsync()
    {
        return await _categoryRepository.GetKpirCategoryCountAsync();
    }

    public async Task<int> GetTotalCategoryCountAsync()
    {
        return await _categoryRepository.CountAsync();
    }

    // Utility methods
    public async Task<bool> CategoryExistsAsync(int id)
    {
        return await _categoryRepository.ExistsAsync(c => c.CategoryId == id);
    }

    public CategoryDto MapToDto(Category category)
    {
        return new CategoryDto
        {
            CategoryId = category.CategoryId,
            CategoryEnglish = category.CategoryEnglish,
            CategoryFrench = category.CategoryFrench,
            DescriptionEnglish = category.DescriptionEnglish,
            DescriptionFrench = category.DescriptionFrench,
            ShortDescriptionEnglish = category.ShortDescriptionEnglish,
            ShortDescriptionFrench = category.ShortDescriptionFrench,
            SortOrderEnglish = category.SortOrderEnglish,
            SortOrderFrench = category.SortOrderFrench,
            KpirFlag = category.KpirFlag,
            ActiveFlag = category.ActiveFlag
        };
    }

    public Category MapToEntity(CategoryFormDto categoryForm)
    {
        return new Category
        {
            CategoryEnglish = categoryForm.CategoryEnglish,
            CategoryFrench = categoryForm.CategoryFrench,
            DescriptionEnglish = categoryForm.DescriptionEnglish,
            DescriptionFrench = categoryForm.DescriptionFrench,
            ShortDescriptionEnglish = categoryForm.ShortDescriptionEnglish,
            ShortDescriptionFrench = categoryForm.ShortDescriptionFrench,
            SortOrderEnglish = categoryForm.SortOrderEnglish,
            SortOrderFrench = categoryForm.SortOrderFrench,
            KpirFlag = categoryForm.KpirFlag,
            ActiveFlag = categoryForm.ActiveFlag
        };
    }

    private CategoryListDto MapToListDto(Category category)
    {
        return new CategoryListDto
        {
            CategoryId = category.CategoryId,
            CategoryEnglish = category.CategoryEnglish,
            CategoryFrench = category.CategoryFrench,
            ShortDescriptionEnglish = category.ShortDescriptionEnglish,
            ShortDescriptionFrench = category.ShortDescriptionFrench,
            ActiveFlag = category.ActiveFlag,
            KpirFlag = category.KpirFlag
        };
    }

    private async Task ValidateCreateCategoryAsync(CategoryFormDto categoryForm)
    {
        // Add validation logic here
        if (!string.IsNullOrEmpty(categoryForm.CategoryEnglish))
        {
            if (await CategoryNameExistsAsync(categoryForm.CategoryEnglish, "en"))
            {
                throw new ArgumentException("Category name already exists in English");
            }
        }

        if (!string.IsNullOrEmpty(categoryForm.CategoryFrench))
        {
            if (await CategoryNameExistsAsync(categoryForm.CategoryFrench, "fr"))
            {
                throw new ArgumentException("Category name already exists in French");
            }
        }
    }

    private async Task ValidateUpdateCategoryAsync(int id, CategoryFormDto categoryForm)
    {
        // Add validation logic here
        if (!string.IsNullOrEmpty(categoryForm.CategoryEnglish))
        {
            if (await CategoryNameExistsAsync(categoryForm.CategoryEnglish, "en", id))
            {
                throw new ArgumentException("Category name already exists in English");
            }
        }

        if (!string.IsNullOrEmpty(categoryForm.CategoryFrench))
        {
            if (await CategoryNameExistsAsync(categoryForm.CategoryFrench, "fr", id))
            {
                throw new ArgumentException("Category name already exists in French");
            }
        }
    }

    private void UpdateCategoryProperties(Category category, CategoryFormDto categoryForm)
    {
        category.CategoryEnglish = categoryForm.CategoryEnglish;
        category.CategoryFrench = categoryForm.CategoryFrench;
        category.DescriptionEnglish = categoryForm.DescriptionEnglish;
        category.DescriptionFrench = categoryForm.DescriptionFrench;
        category.ShortDescriptionEnglish = categoryForm.ShortDescriptionEnglish;
        category.ShortDescriptionFrench = categoryForm.ShortDescriptionFrench;
        category.SortOrderEnglish = categoryForm.SortOrderEnglish;
        category.SortOrderFrench = categoryForm.SortOrderFrench;
        category.KpirFlag = categoryForm.KpirFlag;
        category.ActiveFlag = categoryForm.ActiveFlag;
    }

    // Placeholder implementations for interface completeness
    public Task<(IEnumerable<CategoryListDto> Categories, int TotalCount)> GetCategoriesPagedAsync(int pageNumber, int pageSize, string? searchTerm = null, string language = "en", bool? activeOnly = null, bool? kpirFlag = null)
    {
        throw new NotImplementedException("Will be implemented in next iteration");
    }

    public Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryIds, bool activeStatus)
    {
        return _categoryRepository.BulkUpdateActiveStatusAsync(categoryIds, activeStatus);
    }

    public Task<int> BulkUpdateKpirFlagAsync(IEnumerable<int> categoryIds, bool? kpirFlag)
    {
        return _categoryRepository.BulkUpdateKpirFlagAsync(categoryIds, kpirFlag);
    }

    public Task<int> BulkDeleteCategoriesAsync(IEnumerable<int> categoryIds)
    {
        throw new NotImplementedException("Will be implemented in next iteration");
    }

    public Task<IEnumerable<CategoryDto>> ImportCategoriesAsync(IEnumerable<CategoryFormDto> categories)
    {
        throw new NotImplementedException("Will be implemented in next iteration");
    }

    public Task<IEnumerable<CategoryDto>> ExportCategoriesAsync(bool activeOnly = true)
    {
        throw new NotImplementedException("Will be implemented in next iteration");
    }
}

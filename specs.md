- **Purpose:** A web-based interface to manage hierarchical (multi-level parent/child) data in SQL Server.
- **Tech Stack:**
  - Backend: .NET Core (ASP.NET Core MVC )
  - Frontend: Razor Pages
  - Database: SQL Server 2016
  - ORM: Entity Framework Core
- **Hosting:**
  - Web Server: IIS (Internet Information Services)
  - Database Server: SQL Server 2016

  ## 1. **CRUD Functionality ([Per Entity])**

- **Listing:** View paginated lists of entities.
- **Details:** View details of an entity, including its related children.
- **Create:** Add new entities, choosing parent entities as needed.
- **Update/Edit:** Modify data, including changing parent assignments.
- **Delete:** Remove entities, handling cascading deletes or restricting as appropriate.

**Special Considerations:**
- Prevent deletion of parents if children exist (or confirm cascade).
- Support bulk actions (optional).

## 2. **UI/UX Requirements**

**Navigation**
- Hierarchical navigation (e.g., tree views or master-detail pages).
- Parent entities lead to child listings.

**Forms & Validation**
- Dynamic dropdowns/popups for selecting parents.
- Field validation (required, format, length).
- Error handling and user feedback.

**Other Features**
- Search/filter capabilities in listings.
- Sorting and paging.
- Modal dialogs or dedicated pages for CRUD forms.

## 3. **Backend Specifications**

- **Entity Relationships**: Use EF Core models with navigation properties.
- **Services:** Implement services for business logic (CRUD, cascading deletes, etc.).
- **Data Validation:** Server-side validation.

## 4. **Security**

- **Authentication:** Use ASP.NET Core Identity or similar.
- **Authorization:** Role-based or claims-based to restrict CRUD operations per entity.
- **Audit Trails:** Optionally log changes (for tracking edits/deletes).